<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="58px" font-weight="bold" fill="#005A9E">
    解决方案：启动"故事模式"
  </text>
  
  <!-- 核心理念 -->
  <g transform="translate(200, 220)">
    <rect x="0" y="0" width="1520" height="120" fill="#4CAF50" rx="15" opacity="0.1" />
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" fill="#4CAF50" font-weight="bold">
      抛弃"说明文"，拥抱"英雄之旅"
    </text>
    <text x="760" y="85" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
      启动人类心智的"故事模式"
    </text>
  </g>
  
  <!-- 英雄之旅环形结构 -->
  <g transform="translate(960, 540)">
    <!-- 外圈 -->
    <circle cx="0" cy="0" r="200" stroke="#005A9E" stroke-width="6" fill="none" />
    <circle cx="0" cy="0" r="180" stroke="#F5A623" stroke-width="3" fill="none" opacity="0.5" />
    
    <!-- 起点：平凡世界 -->
    <g transform="translate(0, -200)">
      <circle cx="0" cy="0" r="25" fill="#2196F3" />
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">
        起点
      </text>
      <text x="0" y="-40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333" font-weight="bold">
        平凡世界
      </text>
    </g>
    
    <!-- 冒险召唤 -->
    <g transform="translate(141, -141)">
      <circle cx="0" cy="0" r="20" fill="#FF9800" />
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FFFFFF" font-weight="bold">
        召唤
      </text>
      <text x="30" y="-20" text-anchor="start" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        冒险召唤
      </text>
    </g>
    
    <!-- 导师出现 -->
    <g transform="translate(200, 0)">
      <circle cx="0" cy="0" r="25" fill="#FFD700" />
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333" font-weight="bold">
        导师
      </text>
      <text x="40" y="5" text-anchor="start" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333" font-weight="bold">
        导师出现
      </text>
    </g>
    
    <!-- 跨越门槛 -->
    <g transform="translate(141, 141)">
      <circle cx="0" cy="0" r="20" fill="#9C27B0" />
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FFFFFF" font-weight="bold">
        门槛
      </text>
      <text x="30" y="30" text-anchor="start" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        跨越门槛
      </text>
    </g>
    
    <!-- 试炼磨难 -->
    <g transform="translate(0, 200)">
      <circle cx="0" cy="0" r="25" fill="#FF6B6B" />
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">
        试炼
      </text>
      <text x="0" y="45" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333" font-weight="bold">
        试炼磨难
      </text>
    </g>
    
    <!-- 获得神器 -->
    <g transform="translate(-141, 141)">
      <circle cx="0" cy="0" r="20" fill="#4CAF50" />
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FFFFFF" font-weight="bold">
        神器
      </text>
      <text x="-30" y="30" text-anchor="end" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        获得神器
      </text>
    </g>
    
    <!-- 归来 -->
    <g transform="translate(-200, 0)">
      <circle cx="0" cy="0" r="25" fill="#00BCD4" />
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">
        归来
      </text>
      <text x="-40" y="5" text-anchor="end" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333" font-weight="bold">
        英雄归来
      </text>
    </g>
    
    <!-- 胜利 -->
    <g transform="translate(-141, -141)">
      <circle cx="0" cy="0" r="20" fill="#FFD700" />
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333" font-weight="bold">
        胜利
      </text>
      <text x="-30" y="-20" text-anchor="end" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        获得胜利
      </text>
    </g>
    
    <!-- 中心文字 -->
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#005A9E" font-weight="bold">
      英雄之旅
    </text>
    <text x="0" y="15" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      Hero's Journey
    </text>
  </g>
  
  <!-- 角色定位 -->
  <g transform="translate(200, 780)">
    <rect x="0" y="0" width="1520" height="200" fill="#F5A623" rx="15" opacity="0.1" />
    
    <g transform="translate(100, 50)">
      <!-- 客户是英雄 -->
      <g transform="translate(0, 0)">
        <circle cx="100" cy="50" r="40" fill="#4CAF50" />
        <rect x="80" y="90" width="40" height="60" fill="#005A9E" rx="8" />
        
        <!-- 英雄光环 -->
        <circle cx="100" cy="50" r="50" stroke="#FFD700" stroke-width="3" fill="none" opacity="0.7" />
        
        <!-- 剑 -->
        <rect x="150" y="45" width="30" height="6" fill="#C0C0C0" rx="3" />
        <rect x="175" y="40" width="8" height="16" fill="#8B4513" rx="2" />
        
        <text x="100" y="170" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#4CAF50" font-weight="bold">
          你的客户是"英雄"
        </text>
      </g>
      
      <!-- 你是导师 -->
      <g transform="translate(400, 0)">
        <circle cx="100" cy="50" r="40" fill="#9C27B0" />
        <rect x="80" y="90" width="40" height="60" fill="#333333" rx="8" />
        
        <!-- 导师帽 -->
        <path d="M 70 30 Q 100 10 130 30 Q 100 40 70 30" fill="#FFD700" />
        <circle cx="100" cy="25" r="3" fill="#FFD700" />
        
        <!-- 魔法杖 -->
        <rect x="150" y="40" width="3" height="40" fill="#8B4513" />
        <circle cx="151.5" cy="35" r="5" fill="#FFD700" />
        
        <text x="100" y="170" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#9C27B0" font-weight="bold">
          你是"导师"
        </text>
      </g>
      
      <!-- 方案是神器 -->
      <g transform="translate(800, 0)">
        <rect x="50" y="30" width="100" height="80" fill="#E6F3FF" stroke="#2196F3" stroke-width="4" rx="10" />
        
        <!-- 神器光芒 -->
        <g transform="translate(100, 70)">
          <path d="M -30 0 L -20 -10 L -10 0 L -20 10 Z" fill="#FFD700" opacity="0.7" />
          <path d="M 30 0 L 20 -10 L 10 0 L 20 10 Z" fill="#FFD700" opacity="0.7" />
          <path d="M 0 -30 L -10 -20 L 0 -10 L 10 -20 Z" fill="#FFD700" opacity="0.7" />
          <path d="M 0 30 L -10 20 L 0 10 L 10 20 Z" fill="#FFD700" opacity="0.7" />
        </g>
        
        <text x="100" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#2196F3" font-weight="bold">
          解决方案
        </text>
        <text x="100" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
          神器
        </text>
        
        <text x="100" y="170" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#2196F3" font-weight="bold">
          你的方案是"神器"
        </text>
      </g>
    </g>
  </g>
  
  <!-- 左侧说明文对比 -->
  <g transform="translate(100, 400)">
    <rect x="0" y="0" width="300" height="200" fill="#FF6B6B" rx="15" opacity="0.1" />
    <text x="150" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FF6B6B" font-weight="bold">
      传统"说明文"
    </text>
    
    <!-- 文档图标 -->
    <rect x="100" y="50" width="100" height="120" fill="#FFFFFF" stroke="#FF6B6B" stroke-width="3" rx="8" />
    <line x1="120" y1="70" x2="180" y2="70" stroke="#FF6B6B" stroke-width="2" />
    <line x1="120" y1="85" x2="170" y2="85" stroke="#666666" stroke-width="1" />
    <line x1="120" y1="95" x2="175" y2="95" stroke="#666666" stroke-width="1" />
    <line x1="120" y1="105" x2="165" y2="105" stroke="#666666" stroke-width="1" />
    <line x1="120" y1="115" x2="180" y2="115" stroke="#666666" stroke-width="1" />
    <line x1="120" y1="125" x2="160" y2="125" stroke="#666666" stroke-width="1" />
    <line x1="120" y1="135" x2="175" y2="135" stroke="#666666" stroke-width="1" />
    <line x1="120" y1="145" x2="170" y2="145" stroke="#666666" stroke-width="1" />
    
    <text x="150" y="190" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      枯燥乏味
    </text>
  </g>
  
  <!-- 右侧故事模式 -->
  <g transform="translate(1520, 400)">
    <rect x="0" y="0" width="300" height="200" fill="#4CAF50" rx="15" opacity="0.1" />
    <text x="150" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#4CAF50" font-weight="bold">
      "故事模式"
    </text>
    
    <!-- 故事书图标 -->
    <rect x="100" y="50" width="100" height="120" fill="#FFFFFF" stroke="#4CAF50" stroke-width="3" rx="8" />
    
    <!-- 故事元素 -->
    <circle cx="130" cy="80" r="8" fill="#FFD700" />
    <rect x="125" y="95" width="10" height="15" fill="#2196F3" rx="2" />
    
    <circle cx="170" cy="100" r="8" fill="#FF6B6B" />
    <rect x="165" y="115" width="10" height="15" fill="#9C27B0" rx="2" />
    
    <!-- 对话气泡 -->
    <ellipse cx="150" cy="140" rx="25" ry="15" fill="#E8F5E8" stroke="#4CAF50" stroke-width="1" />
    <text x="150" y="145" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8px" fill="#333333">
      故事情节
    </text>
    
    <text x="150" y="190" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      引人入胜
    </text>
  </g>
</svg>
