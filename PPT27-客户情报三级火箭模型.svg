<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="70px" font-weight="bold" fill="#005A9E">
    "客户情报三级火箭"模型
  </text>
  
  <!-- 三级火箭图示 -->
  <g transform="translate(1200, 200)">
    <!-- 火箭主体 -->
    <rect x="50" y="100" width="60" height="300" fill="#CCCCCC" rx="30" />
    
    <!-- 第一级火箭 - 宏观行业情报 -->
    <rect x="40" y="300" width="80" height="100" fill="#4CAF50" rx="10" />
    <text x="80" y="340" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" font-weight="bold" fill="#FFFFFF">第一级</text>
    <text x="80" y="360" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF">宏观行业</text>
    <text x="80" y="380" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF">看懂天气</text>
    
    <!-- 第二级火箭 - 中观公司情报 -->
    <rect x="45" y="200" width="70" height="100" fill="#2196F3" rx="10" />
    <text x="80" y="240" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" font-weight="bold" fill="#FFFFFF">第二级</text>
    <text x="80" y="260" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF">中观公司</text>
    <text x="80" y="280" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF">看懂车况</text>
    
    <!-- 第三级火箭 - 微观决策人情报 -->
    <rect x="50" y="100" width="60" height="100" fill="#F5A623" rx="10" />
    <text x="80" y="140" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" font-weight="bold" fill="#FFFFFF">第三级</text>
    <text x="80" y="160" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF">微观决策人</text>
    <text x="80" y="180" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF">看懂司机</text>
    
    <!-- 火箭头部 -->
    <polygon points="80,100 60,80 100,80" fill="#FF6B6B" />
    
    <!-- 火焰效果 -->
    <g transform="translate(60, 400)">
      <path d="M 10 0 Q 20 10 30 0 Q 25 15 20 25 Q 15 15 10 0" fill="#FF6B6B" opacity="0.8" />
      <path d="M 15 0 Q 20 8 25 0 Q 22 12 20 20 Q 18 12 15 0" fill="#FFD700" opacity="0.8" />
    </g>
    
    <!-- 发射轨迹 -->
    <path d="M 80 80 Q 60 40 40 0" stroke="#F5A623" stroke-width="3" stroke-dasharray="5,5" opacity="0.6" />
  </g>
  
  <!-- 第一级详细说明 -->
  <g transform="translate(200, 250)">
    <rect x="0" y="0" width="350" height="150" fill="#4CAF50" rx="15" opacity="0.1" />
    <text x="175" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#4CAF50">
      第一级：宏观行业情报
    </text>
    <text x="175" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
      (看懂天气)
    </text>
    
    <text x="20" y="90" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      • 政策环境：国家政策、行业规范
    </text>
    <text x="20" y="115" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      • 经济环境：市场趋势、经济周期
    </text>
    <text x="20" y="140" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      • 技术环境：技术发展、创新趋势
    </text>
  </g>
  
  <!-- 第二级详细说明 -->
  <g transform="translate(600, 250)">
    <rect x="0" y="0" width="350" height="150" fill="#2196F3" rx="15" opacity="0.1" />
    <text x="175" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#2196F3">
      第二级：中观公司情报
    </text>
    <text x="175" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
      (看懂车况)
    </text>
    
    <text x="20" y="90" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      • 商业模式：盈利模式、业务架构
    </text>
    <text x="20" y="115" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      • 战略意图：发展规划、战略重点
    </text>
    <text x="20" y="140" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      • 经营状况：财务数据、年报信息
    </text>
  </g>
  
  <!-- 第三级详细说明 -->
  <g transform="translate(200, 450)">
    <rect x="0" y="0" width="750" height="150" fill="#F5A623" rx="15" opacity="0.1" />
    <text x="375" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#F5A623">
      第三级：微观决策人情报
    </text>
    <text x="375" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
      (看懂司机)
    </text>
    
    <text x="20" y="90" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      • KPI考核：关键绩效指标、考核压力
    </text>
    <text x="20" y="115" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      • 职业背景：教育经历、工作履历、专业偏好
    </text>
    <text x="20" y="140" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      • 个人风格：沟通习惯、决策风格、禁忌事项
    </text>
  </g>
  
  <!-- 天气、车况、司机比喻图标 -->
  <g transform="translate(1000, 450)">
    <!-- 天气图标 -->
    <g transform="translate(0, 0)">
      <circle cx="30" cy="30" r="20" fill="#FFD700" />
      <path d="M 60 40 Q 80 30 90 50 Q 85 70 65 65 Q 50 75 40 60 Q 35 45 50 45" fill="#87CEEB" />
      <text x="60" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">天气</text>
    </g>
    
    <!-- 车况图标 -->
    <g transform="translate(150, 0)">
      <rect x="10" y="40" width="60" height="30" fill="#005A9E" rx="5" />
      <circle cx="25" cy="75" r="8" fill="#333333" />
      <circle cx="55" cy="75" r="8" fill="#333333" />
      <rect x="15" y="30" width="50" height="15" fill="#87CEEB" rx="3" />
      <text x="40" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">车况</text>
    </g>
    
    <!-- 司机图标 -->
    <g transform="translate(300, 0)">
      <circle cx="30" cy="25" r="15" fill="#F5A623" />
      <rect x="20" y="40" width="20" height="30" fill="#F5A623" rx="5" />
      <circle cx="25" cy="20" r="3" fill="#FFFFFF" />
      <circle cx="35" cy="20" r="3" fill="#FFFFFF" />
      <path d="M 25 30 Q 30 35 35 30" stroke="#FFFFFF" stroke-width="2" fill="none" />
      <text x="30" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">司机</text>
    </g>
  </g>
  
  <!-- 底部系统化强调 -->
  <rect x="200" y="650" width="1520" height="100" fill="#005A9E" rx="15" />
  <text x="960" y="690" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    体系化情报收集：避免无头苍蝇式乱搜
  </text>
  <text x="960" y="730" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    三级火箭递进式深入：从宏观到微观，层层聚焦
  </text>
  
  <!-- 发射成功标识 -->
  <g transform="translate(1400, 50)">
    <circle cx="50" cy="50" r="40" fill="#4CAF50" opacity="0.2" />
    <text x="50" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" font-weight="bold" fill="#4CAF50">情报</text>
    <text x="50" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" font-weight="bold" fill="#4CAF50">成功</text>
  </g>
</svg>
