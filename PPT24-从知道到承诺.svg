<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="70px" font-weight="bold" fill="#005A9E">
    从"知道"到"承诺"
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="280" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="44px" fill="#F5A623">
    你的宣言，你的力量
  </text>
  
  <!-- 希望和力量的图片区域 -->
  <g transform="translate(600, 350)">
    <!-- 登山者望向远方 -->
    <g transform="translate(0, 0)">
      <!-- 山峰轮廓 -->
      <path d="M 0 200 L 100 50 L 200 80 L 300 30 L 400 60 L 500 20 L 600 40 L 720 200 Z" 
            fill="#CCCCCC" opacity="0.3" />
      <path d="M 50 200 L 150 80 L 250 100 L 350 60 L 450 90 L 550 50 L 650 70 L 720 200 Z" 
            fill="#CCCCCC" opacity="0.2" />
      
      <!-- 登山者剪影 -->
      <g transform="translate(300, 120)">
        <!-- 头部 -->
        <circle cx="20" cy="20" r="12" fill="#333333" />
        <!-- 身体 -->
        <rect x="12" y="32" width="16" height="40" fill="#333333" rx="3" />
        <!-- 手臂 - 指向远方 -->
        <rect x="28" y="40" width="20" height="6" fill="#333333" rx="3" />
        <rect x="4" y="45" width="12" height="6" fill="#333333" rx="3" />
        <!-- 腿部 -->
        <rect x="15" y="72" width="6" height="25" fill="#333333" rx="2" />
        <rect x="23" y="72" width="6" height="25" fill="#333333" rx="2" />
        
        <!-- 登山杖 -->
        <line x1="8" y1="50" x2="5" y2="95" stroke="#8B4513" stroke-width="3" />
      </g>
      
      <!-- 阳光效果 -->
      <g transform="translate(500, 30)">
        <circle cx="0" cy="0" r="30" fill="#FFD700" opacity="0.6" />
        <line x1="-50" y1="0" x2="-35" y2="0" stroke="#FFD700" stroke-width="3" opacity="0.8" />
        <line x1="35" y1="0" x2="50" y2="0" stroke="#FFD700" stroke-width="3" opacity="0.8" />
        <line x1="0" y1="-50" x2="0" y2="-35" stroke="#FFD700" stroke-width="3" opacity="0.8" />
        <line x1="0" y1="35" x2="0" y2="50" stroke="#FFD700" stroke-width="3" opacity="0.8" />
        <line x1="-35" y1="-35" x2="-25" y2="-25" stroke="#FFD700" stroke-width="3" opacity="0.8" />
        <line x1="25" y1="25" x2="35" y2="35" stroke="#FFD700" stroke-width="3" opacity="0.8" />
        <line x1="35" y1="-35" x2="25" y2="-25" stroke="#FFD700" stroke-width="3" opacity="0.8" />
        <line x1="-25" y1="25" x2="-35" y2="35" stroke="#FFD700" stroke-width="3" opacity="0.8" />
      </g>
    </g>
  </g>
  
  <!-- 破土而出的嫩芽 -->
  <g transform="translate(200, 450)">
    <!-- 土壤 -->
    <ellipse cx="100" cy="150" rx="120" ry="30" fill="#8B4513" />
    
    <!-- 嫩芽 -->
    <g transform="translate(100, 120)">
      <!-- 茎 -->
      <rect x="-2" y="0" width="4" height="60" fill="#4CAF50" rx="2" />
      
      <!-- 叶子 -->
      <ellipse cx="-15" cy="20" rx="12" ry="8" fill="#4CAF50" transform="rotate(-30)" />
      <ellipse cx="15" cy="25" rx="12" ry="8" fill="#4CAF50" transform="rotate(30)" />
      
      <!-- 顶部叶子 -->
      <ellipse cx="-8" cy="5" rx="8" ry="6" fill="#4CAF50" transform="rotate(-15)" />
      <ellipse cx="8" cy="8" rx="8" ry="6" fill="#4CAF50" transform="rotate(15)" />
    </g>
    
    <!-- 阳光照射 -->
    <g transform="translate(50, 50)">
      <line x1="0" y1="0" x2="40" y2="60" stroke="#FFD700" stroke-width="2" opacity="0.6" />
      <line x1="10" y1="0" x2="50" y2="60" stroke="#FFD700" stroke-width="2" opacity="0.6" />
      <line x1="20" y1="0" x2="60" y2="60" stroke="#FFD700" stroke-width="2" opacity="0.6" />
    </g>
    
    <text x="100" y="220" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
      破土而出的力量
    </text>
  </g>
  
  <!-- 分享邀请区域 -->
  <g transform="translate(200, 650)">
    <rect x="0" y="0" width="1520" height="120" fill="#F5A623" rx="15" opacity="0.1" />
    <text x="760" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#F5A623">
      分享你的承诺，传递彼此的力量
    </text>
    
    <text x="760" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" fill="#333333">
      邀请2-3位伙伴，自愿分享你的宣言和对自己的全新承诺
    </text>
    
    <text x="760" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
      你的分享，也会给他人带来力量
    </text>
  </g>
  
  <!-- 礼物图标 -->
  <g transform="translate(1400, 450)">
    <!-- 礼品盒 -->
    <rect x="0" y="40" width="80" height="60" fill="#FF6B6B" rx="5" />
    <rect x="0" y="40" width="80" height="20" fill="#FFB6C1" rx="5" />
    
    <!-- 蝴蝶结 -->
    <rect x="35" y="0" width="10" height="100" fill="#FFD700" />
    <rect x="10" y="35" width="60" height="10" fill="#FFD700" />
    
    <!-- 蝴蝶结装饰 -->
    <ellipse cx="25" cy="40" rx="12" ry="8" fill="#FFD700" />
    <ellipse cx="55" cy="40" rx="12" ry="8" fill="#FFD700" />
    <circle cx="40" cy="40" r="6" fill="#FF6B6B" />
    
    <text x="40" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      分享礼物
    </text>
  </g>
  
  <!-- 掌声效果 -->
  <g transform="translate(100, 350)">
    <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" fill="#F5A623">👏</text>
    <text x="40" y="20" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#F5A623">👏</text>
    <text x="80" y="10" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="30px" fill="#F5A623">👏</text>
  </g>
  
  <g transform="translate(1600, 350)">
    <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="30px" fill="#F5A623">👏</text>
    <text x="40" y="15" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" fill="#F5A623">👏</text>
    <text x="80" y="5" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#F5A623">👏</text>
  </g>
  
  <!-- 底部总结 -->
  <rect x="200" y="800" width="1520" height="100" fill="#005A9E" rx="15" />
  <text x="960" y="835" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#FFFFFF">
    今天上午：认知破局 → 身份锚定 → 能力框架 → 转型契约
  </text>
  <text x="960" y="870" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#FFFFFF">
    下午：为新身份装配第一件实战武器——"战场雷达"
  </text>
</svg>
