<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="70px" font-weight="bold" fill="#FF6B6B">
    呈现的"三宗罪"
  </text>
  
  <!-- 三宗罪展示 -->
  <g transform="translate(200, 250)">
    <!-- 第一宗罪：自我为中心 -->
    <g transform="translate(0, 0)">
      <rect x="0" y="0" width="450" height="400" fill="#FF6B6B" rx="15" opacity="0.1" />
      
      <!-- 自恋图标 -->
      <g transform="translate(175, 50)">
        <circle cx="50" cy="50" r="40" fill="#FF6B6B" />
        <circle cx="40" cy="40" r="5" fill="#FFFFFF" />
        <circle cx="60" cy="40" r="5" fill="#FFFFFF" />
        
        <!-- 自恋表情 -->
        <path d="M 35 60 Q 50 70 65 60" stroke="#FFFFFF" stroke-width="3" fill="none" />
        
        <!-- 镜子 -->
        <ellipse cx="100" cy="50" rx="25" ry="35" fill="#E6E6FA" stroke="#333333" stroke-width="3" />
        <ellipse cx="100" cy="50" rx="20" ry="30" fill="#F0F8FF" />
        <rect x="120" y="45" width="15" height="10" fill="#8B4513" rx="2" />
        
        <!-- 反射 -->
        <circle cx="100" cy="50" r="15" fill="#FF6B6B" opacity="0.5" />
      </g>
      
      <text x="225" y="180" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#FF6B6B" font-weight="bold">
        自我为中心
      </text>
      
      <text x="225" y="220" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        通篇讲"我们"，
      </text>
      <text x="225" y="250" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        而不是"你们"
      </text>
      
      <!-- 错误示例 -->
      <rect x="50" y="280" width="350" height="80" fill="#FFFFFF" stroke="#FF6B6B" stroke-width="2" rx="8" />
      <text x="225" y="305" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FF6B6B" font-weight="bold">
        错误示例：
      </text>
      <text x="225" y="325" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        "我们公司有20年经验..."
      </text>
      <text x="225" y="345" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        "我们的产品功能强大..."
      </text>
    </g>
    
    <!-- 第二宗罪：逻辑的陷阱 -->
    <g transform="translate(535, 0)">
      <rect x="0" y="0" width="450" height="400" fill="#FF9800" rx="15" opacity="0.1" />
      
      <!-- 争辩图标 -->
      <g transform="translate(175, 50)">
        <circle cx="50" cy="50" r="40" fill="#FF9800" />
        <circle cx="40" cy="40" r="5" fill="#FFFFFF" />
        <circle cx="60" cy="40" r="5" fill="#FFFFFF" />
        
        <!-- 争辩表情 -->
        <path d="M 35 65 Q 50 55 65 65" stroke="#FFFFFF" stroke-width="3" fill="none" />
        <path d="M 30 30 L 40 35" stroke="#FFFFFF" stroke-width="3" />
        <path d="M 70 30 L 60 35" stroke="#FFFFFF" stroke-width="3" />
        
        <!-- 大脑图标 -->
        <g transform="translate(110, 30)">
          <ellipse cx="0" cy="20" rx="20" ry="25" fill="#FFB6C1" stroke="#333333" stroke-width="2" />
          <path d="M -15 10 Q -10 5 -5 10" stroke="#333333" stroke-width="1" fill="none" />
          <path d="M 5 15 Q 10 10 15 15" stroke="#333333" stroke-width="1" fill="none" />
          <path d="M -10 25 Q 0 20 10 25" stroke="#333333" stroke-width="1" fill="none" />
        </g>
      </g>
      
      <text x="225" y="180" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#FF9800" font-weight="bold">
        逻辑的陷阱
      </text>
      
      <text x="225" y="220" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        企图说服"大脑"，
      </text>
      <text x="225" y="250" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        却忘了打动"人心"
      </text>
      
      <!-- 错误示例 -->
      <rect x="50" y="280" width="350" height="80" fill="#FFFFFF" stroke="#FF9800" stroke-width="2" rx="8" />
      <text x="225" y="305" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FF9800" font-weight="bold">
        错误示例：
      </text>
      <text x="225" y="325" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        "根据数据分析显示..."
      </text>
      <text x="225" y="345" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        "从技术角度来看..."
      </text>
    </g>
    
    <!-- 第三宗罪：信息的诅咒 -->
    <g transform="translate(1070, 0)">
      <rect x="0" y="0" width="450" height="400" fill="#9C27B0" rx="15" opacity="0.1" />
      
      <!-- 天书图标 -->
      <g transform="translate(175, 50)">
        <circle cx="50" cy="50" r="40" fill="#9C27B0" />
        <circle cx="40" cy="40" r="5" fill="#FFFFFF" />
        <circle cx="60" cy="40" r="5" fill="#FFFFFF" />
        
        <!-- 困惑表情 -->
        <circle cx="50" cy="65" r="3" fill="#FFFFFF" />
        <path d="M 30 25 Q 35 20 40 25" stroke="#FFFFFF" stroke-width="2" fill="none" />
        <path d="M 60 25 Q 65 20 70 25" stroke="#FFFFFF" stroke-width="2" fill="none" />
        
        <!-- 天书 -->
        <g transform="translate(110, 30)">
          <rect x="-15" y="0" width="30" height="40" fill="#F5F5DC" stroke="#333333" stroke-width="2" rx="3" />
          <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8px" fill="#333333">
            ∑∫∂
          </text>
          <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8px" fill="#333333">
            αβγ
          </text>
          <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8px" fill="#333333">
            ∞≈≠
          </text>
        </g>
      </g>
      
      <text x="225" y="180" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#9C27B0" font-weight="bold">
        信息的诅咒
      </text>
      
      <text x="225" y="220" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        用你的"专业"，
      </text>
      <text x="225" y="250" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        制造了客户的"困惑"
      </text>
      
      <!-- 错误示例 -->
      <rect x="50" y="280" width="350" height="80" fill="#FFFFFF" stroke="#9C27B0" stroke-width="2" rx="8" />
      <text x="225" y="305" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#9C27B0" font-weight="bold">
        错误示例：
      </text>
      <text x="225" y="325" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        "采用AI算法优化..."
      </text>
      <text x="225" y="345" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        "通过大数据分析..."
      </text>
    </g>
  </g>
  
  <!-- 底部总结 -->
  <rect x="200" y="700" width="1520" height="120" fill="#333333" rx="15" />
  <text x="960" y="740" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    为何你那份完美的方案，却无法打动决策者？
  </text>
  <text x="960" y="780" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF">
    因为我们常常会陷入呈现的"三宗罪"
  </text>
  
  <!-- 警告图标 -->
  <g transform="translate(100, 250)">
    <!-- 警告三角形 -->
    <path d="M 40 20 L 20 70 L 60 70 Z" fill="#FF6B6B" />
    <text x="40" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF" font-weight="bold">
      !
    </text>
    
    <text x="40" y="90" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      警告
    </text>
  </g>
  
  <!-- 悲剧面具 -->
  <g transform="translate(1700, 250)">
    <circle cx="40" cy="40" r="35" fill="#333333" />
    <circle cx="30" cy="30" r="5" fill="#FFFFFF" />
    <circle cx="50" cy="30" r="5" fill="#FFFFFF" />
    
    <!-- 悲伤嘴巴 -->
    <path d="M 25 55 Q 40 45 55 55" stroke="#FFFFFF" stroke-width="3" fill="none" />
    
    <text x="40" y="90" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      悲剧
    </text>
  </g>
</svg>
