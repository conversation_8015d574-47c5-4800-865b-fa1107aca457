<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="62px" font-weight="bold" fill="#005A9E">
    SPIN的灵魂(1)：量化痛苦
  </text>
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" fill="#666666" font-style="italic">
    (The Art of Quantifying Pain)
  </text>
  
  <!-- I暗示问题的核心目的 -->
  <g transform="translate(200, 280)">
    <rect x="0" y="0" width="1520" height="120" fill="#FF6B6B" rx="15" opacity="0.1" />
    <text x="760" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#FF6B6B">
      I (暗示问题) 的核心目的：
    </text>
    
    <g transform="translate(300, 60)">
      <text x="0" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#333333">
        让客户"疼"
      </text>
      <text x="200" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#666666">
        而"疼"，来自于
      </text>
      <text x="450" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FF6B6B">
        量化
      </text>
    </g>
  </g>
  
  <!-- 连环追问技术 -->
  <g transform="translate(200, 450)">
    <text x="0" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#F5A623">
      连环追问技术：
    </text>
    
    <!-- 追问流程 -->
    <g transform="translate(100, 80)">
      <!-- 定性问题 -->
      <rect x="0" y="0" width="200" height="80" fill="#4CAF50" rx="10" />
      <text x="100" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#FFFFFF">
        定性问题
      </text>
      <text x="100" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#FFFFFF">
        "有什么问题？"
      </text>
      
      <!-- 箭头 -->
      <path d="M 210 40 L 250 40" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
      
      <!-- 定量问题 -->
      <rect x="260" y="0" width="200" height="80" fill="#2196F3" rx="10" />
      <text x="360" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#FFFFFF">
        定量问题
      </text>
      <text x="360" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#FFFFFF">
        "多少次？多久？"
      </text>
      
      <!-- 箭头 -->
      <path d="M 470 40 L 510 40" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
      
      <!-- 财务影响 -->
      <rect x="520" y="0" width="200" height="80" fill="#FF9800" rx="10" />
      <text x="620" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#FFFFFF">
        财务影响
      </text>
      <text x="620" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#FFFFFF">
        "成本多少？"
      </text>
      
      <!-- 箭头 -->
      <path d="M 730 40 L 770 40" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
      
      <!-- 业务影响 -->
      <rect x="780" y="0" width="200" height="80" fill="#9C27B0" rx="10" />
      <text x="880" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#FFFFFF">
        业务影响
      </text>
      <text x="880" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#FFFFFF">
        "对业务的冲击？"
      </text>
    </g>
  </g>
  
  <!-- 痛苦量化示例 -->
  <g transform="translate(200, 600)">
    <rect x="0" y="0" width="1520" height="180" fill="#1A237E" rx="15" opacity="0.1" />
    <text x="760" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#1A237E">
      痛苦量化示例：错发货问题
    </text>
    
    <!-- 量化过程 -->
    <g transform="translate(50, 50)">
      <!-- 步骤1 -->
      <g transform="translate(0, 0)">
        <circle cx="15" cy="15" r="12" fill="#4CAF50" />
        <text x="15" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FFFFFF" font-weight="bold">1</text>
        <text x="40" y="12" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
          "每周十几次"
        </text>
        <text x="40" y="28" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#666666">
          (模糊频率)
        </text>
      </g>
      
      <!-- 箭头 -->
      <path d="M 180 15 L 210 15" stroke="#F5A623" stroke-width="3" marker-end="url(#arrowhead)" />
      
      <!-- 步骤2 -->
      <g transform="translate(220, 0)">
        <circle cx="15" cy="15" r="12" fill="#2196F3" />
        <text x="15" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FFFFFF" font-weight="bold">2</text>
        <text x="40" y="12" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
          "一次500块成本"
        </text>
        <text x="40" y="28" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#666666">
          (单次成本)
        </text>
      </g>
      
      <!-- 箭头 -->
      <path d="M 420 15 L 450 15" stroke="#F5A623" stroke-width="3" marker-end="url(#arrowhead)" />
      
      <!-- 步骤3 -->
      <g transform="translate(460, 0)">
        <circle cx="15" cy="15" r="12" fill="#FF9800" />
        <text x="15" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FFFFFF" font-weight="bold">3</text>
        <text x="40" y="12" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
          "一个月2万多直接损失"
        </text>
        <text x="40" y="28" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#666666">
          (月度损失)
        </text>
      </g>
      
      <!-- 箭头 -->
      <path d="M 720 15 L 750 15" stroke="#F5A623" stroke-width="3" marker-end="url(#arrowhead)" />
      
      <!-- 步骤4 -->
      <g transform="translate(760, 0)">
        <circle cx="15" cy="15" r="12" fill="#FF6B6B" />
        <text x="15" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FFFFFF" font-weight="bold">4</text>
        <text x="40" y="12" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
          "10%的客户流失率"
        </text>
        <text x="40" y="28" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#666666">
          (业务冲击)
        </text>
      </g>
    </g>
    
    <!-- 震撼效果 -->
    <g transform="translate(1200, 80)">
      <circle cx="50" cy="50" r="40" fill="#FF6B6B" opacity="0.2" />
      <text x="50" y="45" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FF6B6B" font-weight="bold">
        触目
      </text>
      <text x="50" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FF6B6B" font-weight="bold">
        惊心
      </text>
    </g>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#F5A623" />
    </marker>
  </defs>
  
  <!-- 核心金句 -->
  <rect x="200" y="820" width="1520" height="120" fill="#005A9E" rx="15" />
  <text x="960" y="860" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    SPIN的四个字母，就像一首交响乐的四个乐章
  </text>
  <text x="960" y="900" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FFD700">
    普通销售死在P，优秀顾问赢在I
  </text>
  
  <!-- 疼痛图标 -->
  <g transform="translate(100, 600)">
    <circle cx="40" cy="40" r="35" fill="#FF6B6B" opacity="0.2" />
    <text x="40" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FF6B6B">
      疼
    </text>
  </g>
  
  <!-- 量化图标 -->
  <g transform="translate(1700, 600)">
    <circle cx="40" cy="40" r="35" fill="#F5A623" opacity="0.2" />
    <text x="40" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#F5A623">
      123
    </text>
    <text x="40" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#F5A623">
      量化
    </text>
  </g>
</svg>
