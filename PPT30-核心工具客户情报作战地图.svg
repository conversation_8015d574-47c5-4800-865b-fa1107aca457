<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="66px" font-weight="bold" fill="#005A9E">
    核心工具：《客户情报作战地图》
  </text>
  
  <!-- 四象限图 -->
  <g transform="translate(400, 200)">
    <!-- 外框 -->
    <rect x="0" y="0" width="1120" height="700" fill="#FFFFFF" stroke="#005A9E" stroke-width="4" rx="15" />
    
    <!-- 中央分割线 -->
    <line x1="560" y1="0" x2="560" y2="700" stroke="#005A9E" stroke-width="3" />
    <line x1="0" y1="350" x2="1120" y2="350" stroke="#005A9E" stroke-width="3" />
    
    <!-- 象限一：外部环境 -->
    <rect x="20" y="20" width="520" height="310" fill="#4CAF50" rx="10" opacity="0.1" />
    <text x="280" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#4CAF50">
      象限一：外部环境
    </text>
    <text x="280" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
      (行业/宏观) → 珍珠
    </text>
    
    <!-- 珍珠图标 -->
    <g transform="translate(50, 100)">
      <circle cx="30" cy="30" r="15" fill="#E6E6FA" stroke="#4CAF50" stroke-width="2" />
      <circle cx="30" cy="30" r="8" fill="#FFFFFF" opacity="0.8" />
      <text x="30" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">政策</text>
      
      <circle cx="100" cy="50" r="15" fill="#E6E6FA" stroke="#4CAF50" stroke-width="2" />
      <circle cx="100" cy="50" r="8" fill="#FFFFFF" opacity="0.8" />
      <text x="100" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">经济</text>
      
      <circle cx="170" cy="30" r="15" fill="#E6E6FA" stroke="#4CAF50" stroke-width="2" />
      <circle cx="170" cy="30" r="8" fill="#FFFFFF" opacity="0.8" />
      <text x="170" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">技术</text>
    </g>
    
    <text x="280" y="300" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#666666" font-style="italic">
      事实的陈列区
    </text>
    
    <!-- 象限二：内部现状 -->
    <rect x="580" y="20" width="520" height="310" fill="#2196F3" rx="10" opacity="0.1" />
    <text x="840" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#2196F3">
      象限二：内部现状
    </text>
    <text x="840" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
      (公司/决策人) → 珍珠
    </text>
    
    <!-- 珍珠图标 -->
    <g transform="translate(610, 100)">
      <circle cx="30" cy="30" r="15" fill="#E6E6FA" stroke="#2196F3" stroke-width="2" />
      <circle cx="30" cy="30" r="8" fill="#FFFFFF" opacity="0.8" />
      <text x="30" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">年报</text>
      
      <circle cx="100" cy="50" r="15" fill="#E6E6FA" stroke="#2196F3" stroke-width="2" />
      <circle cx="100" cy="50" r="8" fill="#FFFFFF" opacity="0.8" />
      <text x="100" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">战略</text>
      
      <circle cx="170" cy="30" r="15" fill="#E6E6FA" stroke="#2196F3" stroke-width="2" />
      <circle cx="170" cy="30" r="8" fill="#FFFFFF" opacity="0.8" />
      <text x="170" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">KPI</text>
    </g>
    
    <text x="840" y="300" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#666666" font-style="italic">
      事实的陈列区
    </text>
    
    <!-- 象限三：机会与切入点 -->
    <rect x="20" y="370" width="520" height="310" fill="#F5A623" rx="10" opacity="0.1" />
    <text x="280" y="400" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#F5A623">
      象限三：机会与切入点
    </text>
    <text x="280" y="430" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
      (我的洞察与观点) → 串线
    </text>
    
    <!-- 炼金炉图标 -->
    <g transform="translate(200, 450)">
      <ellipse cx="60" cy="40" rx="50" ry="30" fill="#FF6B6B" opacity="0.3" />
      <ellipse cx="60" cy="40" rx="35" ry="20" fill="#FFD700" opacity="0.6" />
      <circle cx="60" cy="40" r="15" fill="#FF6B6B" />
      <text x="60" y="45" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FFFFFF" font-weight="bold">炼金炉</text>
      
      <!-- 火焰效果 -->
      <path d="M 40 70 Q 50 80 60 70 Q 70 80 80 70" stroke="#FF6B6B" stroke-width="2" fill="none" />
    </g>
    
    <text x="280" y="580" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#F5A623" font-weight="bold">
      地图的心脏：信息碰撞，提炼洞察
    </text>
    <text x="280" y="650" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#666666" font-style="italic">
      我们卖的，正是这个洞察
    </text>
    
    <!-- 象限四：首次接触策略 -->
    <rect x="580" y="370" width="520" height="310" fill="#9C27B0" rx="10" opacity="0.1" />
    <text x="840" y="400" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#9C27B0">
      象限四：首次接触策略
    </text>
    <text x="840" y="430" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
      (我的提问与行动) → 项链
    </text>
    
    <!-- 弹药库图标 -->
    <g transform="translate(760, 450)">
      <rect x="0" y="0" width="80" height="60" fill="#9C27B0" rx="5" />
      <rect x="10" y="10" width="60" height="40" fill="#FFFFFF" rx="3" />
      <text x="40" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#9C27B0" font-weight="bold">弹药库</text>
      <text x="40" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8px" fill="#9C27B0">3-5个</text>
      <text x="40" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8px" fill="#9C27B0">关键问题</text>
    </g>
    
    <text x="840" y="580" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#9C27B0" font-weight="bold">
      直击灵魂的开场提问
    </text>
    <text x="840" y="650" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#666666" font-style="italic">
      决定第一次拜访的成败
    </text>
  </g>
  
  <!-- 连接箭头 -->
  <g transform="translate(400, 200)">
    <!-- 从象限一到象限三 -->
    <path d="M 280 330 Q 200 400 280 370" stroke="#F5A623" stroke-width="4" fill="none" marker-end="url(#arrowhead)" />
    
    <!-- 从象限二到象限三 -->
    <path d="M 840 330 Q 920 400 840 370" stroke="#F5A623" stroke-width="4" fill="none" marker-end="url(#arrowhead)" />
    
    <!-- 从象限三到象限四 -->
    <path d="M 540 525 Q 600 525 580 525" stroke="#F5A623" stroke-width="4" fill="none" marker-end="url(#arrowhead)" />
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#F5A623" />
    </marker>
  </defs>
  
  <!-- 底部强调 -->
  <rect x="200" y="950" width="1520" height="80" fill="#005A9E" rx="15" />
  <text x="960" y="1000" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    不是信息清单，而是战略推演沙盘 · 串起珍珠的那根"线"
  </text>
</svg>
