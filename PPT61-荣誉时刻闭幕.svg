<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64px" font-weight="bold" fill="#FFD700">
    荣誉时刻 &amp; 闭幕
  </text>
  
  <!-- 颁奖典礼 -->
  <g transform="translate(200, 220)">
    <rect x="0" y="0" width="1520" height="200" fill="#FFD700" rx="15" opacity="0.1" />
    <text x="760" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FFD700">
      颁奖典礼
    </text>
    
    <!-- 三个奖项 -->
    <g transform="translate(100, 70)">
      <!-- 最佳导演奖 -->
      <g transform="translate(0, 0)">
        <rect x="0" y="0" width="400" height="100" fill="#9C27B0" rx="10" opacity="0.1" />
        
        <!-- 奖杯 -->
        <g transform="translate(50, 20)">
          <ellipse cx="30" cy="40" rx="20" ry="12" fill="#FFD700" />
          <rect x="20" y="30" width="20" height="20" fill="#FFD700" rx="4" />
          <ellipse cx="30" cy="30" rx="12" ry="6" fill="#FFF8DC" />
          <rect x="25" y="52" width="10" height="6" fill="#8B4513" rx="3" />
        </g>
        
        <text x="200" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#9C27B0" font-weight="bold">
          最佳导演奖
        </text>
        <text x="200" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
          (方案逻辑)
        </text>
      </g>
      
      <!-- 最佳主演奖 -->
      <g transform="translate(460, 0)">
        <rect x="0" y="0" width="400" height="100" fill="#FF6B6B" rx="10" opacity="0.1" />
        
        <!-- 奖杯 -->
        <g transform="translate(50, 20)">
          <ellipse cx="30" cy="40" rx="20" ry="12" fill="#FFD700" />
          <rect x="20" y="30" width="20" height="20" fill="#FFD700" rx="4" />
          <ellipse cx="30" cy="30" rx="12" ry="6" fill="#FFF8DC" />
          <rect x="25" y="52" width="10" height="6" fill="#8B4513" rx="3" />
        </g>
        
        <text x="200" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FF6B6B" font-weight="bold">
          最佳主演奖
        </text>
        <text x="200" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
          (呈现效果)
        </text>
      </g>
      
      <!-- 最具价值影片奖 -->
      <g transform="translate(920, 0)">
        <rect x="0" y="0" width="400" height="100" fill="#4CAF50" rx="10" opacity="0.1" />
        
        <!-- 奖杯 -->
        <g transform="translate(50, 20)">
          <ellipse cx="30" cy="40" rx="20" ry="12" fill="#FFD700" />
          <rect x="20" y="30" width="20" height="20" fill="#FFD700" rx="4" />
          <ellipse cx="30" cy="30" rx="12" ry="6" fill="#FFF8DC" />
          <rect x="25" y="52" width="10" height="6" fill="#8B4513" rx="3" />
        </g>
        
        <text x="200" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#4CAF50" font-weight="bold">
          最具价值影片奖
        </text>
        <text x="200" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
          (商业价值)
        </text>
      </g>
    </g>
  </g>
  
  <!-- 奥斯卡小金人 -->
  <g transform="translate(960, 480)">
    <!-- 小金人身体 -->
    <circle cx="0" cy="-40" r="20" fill="#FFD700" />
    <rect x="-15" y="-20" width="30" height="60" fill="#FFD700" rx="8" />
    
    <!-- 小金人头部 -->
    <circle cx="0" cy="-60" r="15" fill="#FFD700" />
    
    <!-- 小金人手臂 -->
    <line x1="-15" y1="-10" x2="-25" y2="0" stroke="#FFD700" stroke-width="6" />
    <line x1="15" y1="-10" x2="25" y2="0" stroke="#FFD700" stroke-width="6" />
    
    <!-- 小金人腿部 -->
    <line x1="-10" y1="40" x2="-15" y2="70" stroke="#FFD700" stroke-width="6" />
    <line x1="10" y1="40" x2="15" y2="70" stroke="#FFD700" stroke-width="6" />
    
    <!-- 底座 -->
    <rect x="-25" y="70" width="50" height="15" fill="#8B4513" rx="5" />
    
    <!-- 光芒效果 -->
    <g transform="translate(0, -40)">
      <path d="M -40 0 L -30 -8 L -20 0 L -30 8 Z" fill="#FFD700" opacity="0.5" />
      <path d="M 40 0 L 30 -8 L 20 0 L 30 8 Z" fill="#FFD700" opacity="0.5" />
      <path d="M 0 -40 L -8 -30 L 0 -20 L 8 -30 Z" fill="#FFD700" opacity="0.5" />
      <path d="M 0 40 L -8 30 L 0 20 L 8 30 Z" fill="#FFD700" opacity="0.5" />
    </g>
  </g>
  
  <!-- 全副武装宣言 -->
  <g transform="translate(200, 600)">
    <rect x="0" y="0" width="1520" height="120" fill="#005A9E" rx="15" />
    <text x="760" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#FFFFFF">
      我们，全副武装！
    </text>
    <text x="760" y="90" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF">
      We Are Fully Armed!
    </text>
  </g>
  
  <!-- 最终总结与升华 -->
  <g transform="translate(200, 750)">
    <rect x="0" y="0" width="1520" height="240" fill="#F5A623" rx="15" />
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
      各位导演、各位主演！祝贺大家！
    </text>
    
    <text x="760" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" fill="#FFFFFF">
      在过去这紧张而又充实的两天里，我们共同完成了一次不可思议的旅程
    </text>
    
    <g transform="translate(100, 100)">
      <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF">
        • 我们打破了旧我，锚定了新身份
      </text>
      <text x="0" y="55" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF">
        • 我们装备了"雷达"，学会了"诊断"
      </text>
      <text x="700" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF">
        • 我们绘制了"蓝图"，更学会了如何演绎动人心魄的"商业大片"
      </text>
      <text x="700" y="55" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF">
        • 今晚，你们已经全副武装
      </text>
    </g>
    
    <rect x="100" y="180" width="1320" height="40" fill="#FF6B6B" rx="8" />
    <text x="760" y="205" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#FFFFFF">
      但请记住，这间教室，只是安全的"训练场"。从明天开始，才是真正的"战场"！
    </text>
  </g>
  
  <!-- 左侧装备图标 -->
  <g transform="translate(100, 480)">
    <!-- 雷达 -->
    <circle cx="40" cy="40" r="30" stroke="#4CAF50" stroke-width="4" fill="none" />
    <circle cx="40" cy="40" r="20" stroke="#4CAF50" stroke-width="2" fill="none" />
    <circle cx="40" cy="40" r="10" stroke="#4CAF50" stroke-width="2" fill="none" />
    <line x1="40" y1="40" x2="60" y2="25" stroke="#4CAF50" stroke-width="3" />
    
    <text x="40" y="90" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      客户雷达
    </text>
  </g>
  
  <!-- 右侧蓝图图标 -->
  <g transform="translate(1700, 480)">
    <!-- 蓝图 -->
    <rect x="10" y="20" width="60" height="40" fill="#E6F3FF" stroke="#2196F3" stroke-width="3" rx="5" />
    <line x1="20" y1="30" x2="60" y2="30" stroke="#2196F3" stroke-width="2" />
    <line x1="20" y1="40" x2="50" y2="40" stroke="#2196F3" stroke-width="1" />
    <line x1="20" y1="50" x2="55" y2="50" stroke="#2196F3" stroke-width="1" />
    
    <text x="40" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      业务蓝图
    </text>
  </g>
  
  <!-- 庆祝烟花效果 -->
  <g transform="translate(300, 100)">
    <g transform="translate(0, 0)">
      <circle cx="0" cy="0" r="3" fill="#FFD700" />
      <path d="M 0 0 L -15 -15 M 0 0 L 15 -15 M 0 0 L -15 15 M 0 0 L 15 15" stroke="#FFD700" stroke-width="2" />
    </g>
    
    <g transform="translate(100, -20)">
      <circle cx="0" cy="0" r="2" fill="#FF6B6B" />
      <path d="M 0 0 L -10 -10 M 0 0 L 10 -10 M 0 0 L -10 10 M 0 0 L 10 10" stroke="#FF6B6B" stroke-width="1" />
    </g>
    
    <g transform="translate(200, 10)">
      <circle cx="0" cy="0" r="2" fill="#4CAF50" />
      <path d="M 0 0 L -8 -8 M 0 0 L 8 -8 M 0 0 L -8 8 M 0 0 L 8 8" stroke="#4CAF50" stroke-width="1" />
    </g>
  </g>
  
  <g transform="translate(1400, 120)">
    <g transform="translate(0, 0)">
      <circle cx="0" cy="0" r="3" fill="#9C27B0" />
      <path d="M 0 0 L -12 -12 M 0 0 L 12 -12 M 0 0 L -12 12 M 0 0 L 12 12" stroke="#9C27B0" stroke-width="2" />
    </g>
    
    <g transform="translate(-80, 30)">
      <circle cx="0" cy="0" r="2" fill="#FF9800" />
      <path d="M 0 0 L -8 -8 M 0 0 L 8 -8 M 0 0 L -8 8 M 0 0 L 8 8" stroke="#FF9800" stroke-width="1" />
    </g>
  </g>
</svg>
