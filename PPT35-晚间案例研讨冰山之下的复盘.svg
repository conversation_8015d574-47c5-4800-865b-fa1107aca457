<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="66px" font-weight="bold" fill="#005A9E">
    晚间案例研讨："冰山之下"的复盘
  </text>
  
  <!-- 深邃冰山图示 -->
  <g transform="translate(600, 250)">
    <!-- 深海背景 -->
    <rect x="0" y="0" width="720" height="500" fill="#001122" rx="15" opacity="0.8" />
    
    <!-- 海平面 -->
    <line x1="0" y1="150" x2="720" y2="150" stroke="#4A90E2" stroke-width="6" />
    <text x="730" y="155" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#4A90E2">海平面</text>
    
    <!-- 冰山水面上部分 -->
    <path d="M 250 80 L 470 80 L 500 150 L 220 150 Z" fill="#E6F7FF" stroke="#87CEEB" stroke-width="3" />
    <text x="360" y="120" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#005A9E" font-weight="bold">
      表面现象
    </text>
    
    <!-- 冰山水面下巨大部分 -->
    <path d="M 220 150 L 500 150 L 600 450 L 120 450 Z" fill="#B3E5FC" stroke="#2196F3" stroke-width="3" />
    
    <!-- 神秘阴影区域 -->
    <ellipse cx="360" cy="300" rx="150" ry="100" fill="#1A237E" opacity="0.6" />
    <text x="360" y="290" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF" font-weight="bold">
      致命根因
    </text>
    <text x="360" y="320" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF">
      隐藏在深处
    </text>
    
    <!-- 水波纹效果 -->
    <path d="M 50 160 Q 150 155 250 160 Q 350 165 450 160 Q 550 155 650 160" 
          stroke="#4A90E2" stroke-width="3" fill="none" opacity="0.7" />
    <path d="M 50 175 Q 150 170 250 175 Q 350 180 450 175 Q 550 170 650 175" 
          stroke="#4A90E2" stroke-width="2" fill="none" opacity="0.5" />
    <path d="M 50 190 Q 150 185 250 190 Q 350 195 450 190 Q 550 185 650 190" 
          stroke="#4A90E2" stroke-width="2" fill="none" opacity="0.3" />
    
    <!-- 探照灯效果 -->
    <path d="M 360 50 L 320 300 L 400 300 Z" fill="#FFD700" opacity="0.3" />
    <circle cx="360" cy="50" r="15" fill="#FFD700" />
  </g>
  
  <!-- 案例背景 -->
  <g transform="translate(200, 280)">
    <text x="0" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#F5A623">
      背景：
    </text>
    <text x="120" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" fill="#333333">
      优势巨大，志在必得
    </text>
    
    <text x="0" y="100" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#FF6B6B">
      结果：
    </text>
    <text x="120" y="100" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" fill="#333333">
      意外丢单，兵败麦城
    </text>
    
    <text x="0" y="160" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#9C27B0">
      任务：
    </text>
    <text x="120" y="160" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" fill="#333333">
      探寻冰山之下的致命根因
    </text>
  </g>
  
  <!-- 悬疑氛围元素 -->
  <g transform="translate(100, 500)">
    <!-- 问号 -->
    <circle cx="50" cy="50" r="40" fill="#FF6B6B" opacity="0.2" />
    <text x="50" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48px" font-weight="bold" fill="#FF6B6B">
      ?
    </text>
    
    <!-- 放大镜 -->
    <g transform="translate(150, 20)">
      <circle cx="30" cy="30" r="25" stroke="#005A9E" stroke-width="4" fill="none" />
      <line x1="50" y1="50" x2="70" y2="70" stroke="#005A9E" stroke-width="4" />
      <text x="30" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#005A9E" font-weight="bold">
        调查
      </text>
    </g>
    
    <!-- 警告标识 -->
    <g transform="translate(250, 10)">
      <polygon points="30,10 50,50 10,50" fill="#FF6B6B" />
      <polygon points="30,15 45,45 15,45" fill="#FFFFFF" />
      <text x="30" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#FF6B6B">
        !
      </text>
    </g>
  </g>
  
  <!-- 底部氛围营造 -->
  <rect x="200" y="800" width="1520" height="120" fill="#1A237E" rx="15" />
  <text x="960" y="840" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    导致它沉没的真正原因，不在水面之上，而在水面之下
  </text>
  <text x="960" y="880" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFD700">
    请扮演"首席复盘官"的角色，独立思考15分钟
  </text>
  
  <!-- 时间标识 -->
  <g transform="translate(1600, 300)">
    <circle cx="40" cy="40" r="35" stroke="#F5A623" stroke-width="4" fill="#1A237E" />
    <text x="40" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">
      19:00
    </text>
    <text x="40" y="45" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF">
      -21:00
    </text>
    
    <text x="40" y="95" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#F5A623" font-weight="bold">
      深度复盘
    </text>
  </g>
  
  <!-- 神秘光束效果 -->
  <g transform="translate(50, 200)">
    <path d="M 0 0 L 20 100 L -20 100 Z" fill="#FFD700" opacity="0.4" />
    <path d="M 0 0 L 15 80 L -15 80 Z" fill="#FFFFFF" opacity="0.6" />
  </g>
  
  <g transform="translate(1800, 600)">
    <path d="M 0 0 L 20 100 L -20 100 Z" fill="#4A90E2" opacity="0.4" />
    <path d="M 0 0 L 15 80 L -15 80 Z" fill="#FFFFFF" opacity="0.6" />
  </g>
</svg>
