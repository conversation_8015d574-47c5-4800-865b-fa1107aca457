<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="70px" font-weight="bold" fill="#005A9E">
    PPT制作进行中...
  </text>
  
  <!-- 工作场景 -->
  <g transform="translate(200, 250)">
    <!-- 小组A工作场景 -->
    <g transform="translate(0, 0)">
      <rect x="0" y="0" width="450" height="300" fill="#4CAF50" rx="15" opacity="0.1" />
      <text x="225" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#4CAF50">
        小组A - 制造业方案
      </text>
      
      <!-- 工作桌面 -->
      <rect x="50" y="50" width="350" height="200" fill="#F5F5F5" stroke="#4CAF50" stroke-width="2" rx="10" />
      
      <!-- 团队成员 -->
      <g transform="translate(80, 80)">
        <!-- 成员1 - 在电脑前 -->
        <circle cx="30" cy="30" r="20" fill="#4CAF50" />
        <rect x="20" y="50" width="20" height="30" fill="#005A9E" rx="3" />
        
        <!-- 电脑屏幕 -->
        <rect x="60" y="20" width="60" height="40" fill="#333333" rx="3" />
        <rect x="65" y="25" width="50" height="30" fill="#FFFFFF" rx="2" />
        
        <!-- PPT内容模拟 -->
        <rect x="70" y="30" width="40" height="5" fill="#4CAF50" rx="1" />
        <rect x="70" y="38" width="25" height="3" fill="#666666" rx="1" />
        <rect x="70" y="43" width="30" height="3" fill="#666666" rx="1" />
        <rect x="70" y="48" width="20" height="3" fill="#666666" rx="1" />
        
        <text x="90" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
          制作PPT
        </text>
      </g>
      
      <!-- 成员2 - 讨论数据 -->
      <g transform="translate(200, 80)">
        <circle cx="30" cy="30" r="20" fill="#2196F3" />
        <rect x="20" y="50" width="20" height="30" fill="#333333" rx="3" />
        
        <!-- 数据表格 -->
        <rect x="60" y="30" width="50" height="40" fill="#FFFFFF" stroke="#2196F3" stroke-width="1" rx="3" />
        <line x1="65" y1="40" x2="105" y2="40" stroke="#2196F3" stroke-width="1" />
        <line x1="65" y1="50" x2="105" y2="50" stroke="#2196F3" stroke-width="1" />
        <line x1="65" y1="60" x2="105" y2="60" stroke="#2196F3" stroke-width="1" />
        
        <!-- 数据内容 -->
        <text x="70" y="37" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="6px" fill="#333333">ROI</text>
        <text x="70" y="47" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="6px" fill="#333333">180%</text>
        <text x="70" y="57" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="6px" fill="#333333">18月</text>
        
        <text x="85" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
          计算ROI
        </text>
      </g>
      
      <!-- 成员3 - 设计图表 -->
      <g transform="translate(320, 80)">
        <circle cx="30" cy="30" r="20" fill="#FF9800" />
        <rect x="20" y="50" width="20" height="30" fill="#005A9E" rx="3" />
        
        <!-- 图表设计 -->
        <rect x="60" y="25" width="50" height="45" fill="#FFFFFF" stroke="#FF9800" stroke-width="1" rx="3" />
        
        <!-- 柱状图 -->
        <rect x="70" y="55" width="8" height="10" fill="#FF6B6B" />
        <rect x="82" y="45" width="8" height="20" fill="#FF9800" />
        <rect x="94" y="35" width="8" height="30" fill="#4CAF50" />
        
        <text x="85" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
          设计图表
        </text>
      </g>
      
      <!-- 进度指示 -->
      <g transform="translate(50, 270)">
        <rect x="0" y="0" width="350" height="20" fill="#E0E0E0" rx="10" />
        <rect x="0" y="0" width="210" height="20" fill="#4CAF50" rx="10" />
        <text x="175" y="15" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">
          60%
        </text>
      </g>
    </g>
    
    <!-- 小组B工作场景 -->
    <g transform="translate(535, 0)">
      <rect x="0" y="0" width="450" height="300" fill="#2196F3" rx="15" opacity="0.1" />
      <text x="225" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#2196F3">
        小组B - 金融服务方案
      </text>
      
      <!-- 工作桌面 -->
      <rect x="50" y="50" width="350" height="200" fill="#F5F5F5" stroke="#2196F3" stroke-width="2" rx="10" />
      
      <!-- 类似的团队工作场景 -->
      <g transform="translate(80, 80)">
        <circle cx="30" cy="30" r="20" fill="#4CAF50" />
        <rect x="20" y="50" width="20" height="30" fill="#005A9E" rx="3" />
        
        <rect x="60" y="20" width="60" height="40" fill="#333333" rx="3" />
        <rect x="65" y="25" width="50" height="30" fill="#FFFFFF" rx="2" />
        
        <rect x="70" y="30" width="40" height="5" fill="#2196F3" rx="1" />
        <rect x="70" y="38" width="25" height="3" fill="#666666" rx="1" />
        <rect x="70" y="43" width="30" height="3" fill="#666666" rx="1" />
        
        <text x="90" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
          制作PPT
        </text>
      </g>
      
      <g transform="translate(200, 80)">
        <circle cx="30" cy="30" r="20" fill="#2196F3" />
        <rect x="20" y="50" width="20" height="30" fill="#333333" rx="3" />
        
        <!-- 风险评估图 -->
        <rect x="60" y="30" width="50" height="40" fill="#FFFFFF" stroke="#2196F3" stroke-width="1" rx="3" />
        <circle cx="85" cy="50" r="15" fill="#2196F3" opacity="0.3" />
        <text x="85" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8px" fill="#333333">
          风险
        </text>
        
        <text x="85" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
          风险分析
        </text>
      </g>
      
      <g transform="translate(320, 80)">
        <circle cx="30" cy="30" r="20" fill="#FF9800" />
        <rect x="20" y="50" width="20" height="30" fill="#005A9E" rx="3" />
        
        <!-- 合规要求 -->
        <rect x="60" y="25" width="50" height="45" fill="#FFFFFF" stroke="#FF9800" stroke-width="1" rx="3" />
        <rect x="65" y="35" width="40" height="3" fill="#FF9800" rx="1" />
        <rect x="65" y="42" width="30" height="3" fill="#FF9800" rx="1" />
        <rect x="65" y="49" width="35" height="3" fill="#FF9800" rx="1" />
        <rect x="65" y="56" width="25" height="3" fill="#FF9800" rx="1" />
        
        <text x="85" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
          合规检查
        </text>
      </g>
      
      <!-- 进度指示 -->
      <g transform="translate(50, 270)">
        <rect x="0" y="0" width="350" height="20" fill="#E0E0E0" rx="10" />
        <rect x="0" y="0" width="175" height="20" fill="#2196F3" rx="10" />
        <text x="87.5" y="15" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">
          50%
        </text>
      </g>
    </g>
    
    <!-- 小组C工作场景 -->
    <g transform="translate(1070, 0)">
      <rect x="0" y="0" width="450" height="300" fill="#9C27B0" rx="15" opacity="0.1" />
      <text x="225" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#9C27B0">
        小组C - 零售连锁方案
      </text>
      
      <!-- 工作桌面 -->
      <rect x="50" y="50" width="350" height="200" fill="#F5F5F5" stroke="#9C27B0" stroke-width="2" rx="10" />
      
      <!-- 类似的团队工作场景 -->
      <g transform="translate(80, 80)">
        <circle cx="30" cy="30" r="20" fill="#4CAF50" />
        <rect x="20" y="50" width="20" height="30" fill="#005A9E" rx="3" />
        
        <rect x="60" y="20" width="60" height="40" fill="#333333" rx="3" />
        <rect x="65" y="25" width="50" height="30" fill="#FFFFFF" rx="2" />
        
        <rect x="70" y="30" width="40" height="5" fill="#9C27B0" rx="1" />
        <rect x="70" y="38" width="25" height="3" fill="#666666" rx="1" />
        <rect x="70" y="43" width="30" height="3" fill="#666666" rx="1" />
        
        <text x="90" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
          制作PPT
        </text>
      </g>
      
      <g transform="translate(200, 80)">
        <circle cx="30" cy="30" r="20" fill="#2196F3" />
        <rect x="20" y="50" width="20" height="30" fill="#333333" rx="3" />
        
        <!-- 全渠道架构图 -->
        <rect x="60" y="30" width="50" height="40" fill="#FFFFFF" stroke="#9C27B0" stroke-width="1" rx="3" />
        
        <!-- 渠道节点 -->
        <circle cx="75" cy="45" r="5" fill="#9C27B0" />
        <circle cx="95" cy="45" r="5" fill="#9C27B0" />
        <circle cx="85" cy="55" r="5" fill="#9C27B0" />
        
        <!-- 连接线 -->
        <line x1="75" y1="45" x2="95" y2="45" stroke="#9C27B0" stroke-width="1" />
        <line x1="80" y1="50" x2="85" y2="55" stroke="#9C27B0" stroke-width="1" />
        <line x1="90" y1="50" x2="85" y2="55" stroke="#9C27B0" stroke-width="1" />
        
        <text x="85" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
          全渠道设计
        </text>
      </g>
      
      <g transform="translate(320, 80)">
        <circle cx="30" cy="30" r="20" fill="#FF9800" />
        <rect x="20" y="50" width="20" height="30" fill="#005A9E" rx="3" />
        
        <!-- 用户体验流程 -->
        <rect x="60" y="25" width="50" height="45" fill="#FFFFFF" stroke="#FF9800" stroke-width="1" rx="3" />
        
        <!-- 流程步骤 -->
        <rect x="65" y="35" width="8" height="8" fill="#FF9800" rx="2" />
        <rect x="78" y="35" width="8" height="8" fill="#FF9800" rx="2" />
        <rect x="91" y="35" width="8" height="8" fill="#FF9800" rx="2" />
        
        <!-- 箭头 -->
        <path d="M 73 39 L 78 39" stroke="#FF9800" stroke-width="1" marker-end="url(#smallarrow)" />
        <path d="M 86 39 L 91 39" stroke="#FF9800" stroke-width="1" marker-end="url(#smallarrow)" />
        
        <text x="85" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
          体验设计
        </text>
      </g>
      
      <!-- 进度指示 -->
      <g transform="translate(50, 270)">
        <rect x="0" y="0" width="350" height="20" fill="#E0E0E0" rx="10" />
        <rect x="0" y="0" width="245" height="20" fill="#9C27B0" rx="10" />
        <text x="122.5" y="15" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">
          70%
        </text>
      </g>
    </g>
  </g>
  
  <!-- 讲师巡场指导 -->
  <g transform="translate(200, 580)">
    <rect x="0" y="0" width="1520" height="120" fill="#F5A623" rx="15" opacity="0.1" />
    
    <!-- 讲师形象 -->
    <g transform="translate(100, 30)">
      <circle cx="50" cy="50" r="30" fill="#F5A623" />
      <rect x="35" y="80" width="30" height="40" fill="#005A9E" rx="6" />
      
      <!-- 面部特征 -->
      <circle cx="42" cy="45" r="3" fill="#FFFFFF" />
      <circle cx="58" cy="45" r="3" fill="#FFFFFF" />
      <path d="M 42 58 Q 50 63 58 58" stroke="#FFFFFF" stroke-width="2" fill="none" />
      
      <!-- 指导手势 -->
      <line x1="20" y1="65" x2="10" y2="55" stroke="#F5A623" stroke-width="3" />
      <line x1="80" y1="65" x2="90" y2="55" stroke="#F5A623" stroke-width="3" />
      
      <text x="50" y="140" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">
        讲师巡场
      </text>
    </g>
    
    <!-- 指导要点 -->
    <g transform="translate(250, 40)">
      <text x="0" y="20" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#F5A623">
        巡场指导重点：
      </text>
      <text x="30" y="45" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
        • 确保每页PPT都有明确的说服目标
      </text>
      <text x="30" y="70" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
        • 检查数据的真实性和说服力
      </text>
    </g>
  </g>
  
  <!-- 倒计时 -->
  <g transform="translate(1600, 300)">
    <circle cx="60" cy="60" r="50" stroke="#FF6B6B" stroke-width="6" fill="#FFFFFF" />
    <circle cx="60" cy="60" r="5" fill="#FF6B6B" />
    
    <!-- 倒计时显示 -->
    <text x="60" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#FF6B6B" font-weight="bold">
      20:00
    </text>
    <text x="60" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      剩余时间
    </text>
    
    <!-- 动态指针 -->
    <line x1="60" y1="60" x2="60" y2="25" stroke="#FF6B6B" stroke-width="3" />
    <line x1="60" y1="60" x2="85" y2="40" stroke="#005A9E" stroke-width="2" />
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="smallarrow" markerWidth="5" markerHeight="3" refX="4" refY="1.5" orient="auto">
      <polygon points="0 0, 5 1.5, 0 3" fill="#FF9800" />
    </marker>
  </defs>
  
  <!-- 底部激励 -->
  <rect x="200" y="730" width="1520" height="120" fill="#4CAF50" rx="15" />
  <text x="960" y="770" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    每一页PPT都是一个"说服武器"
  </text>
  <text x="960" y="810" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF">
    确保逻辑清晰、数据有力、视觉专业！
  </text>
  
  <!-- 专注图标 -->
  <g transform="translate(100, 580)">
    <!-- 专注状态 -->
    <circle cx="30" cy="30" r="25" fill="#4CAF50" opacity="0.3" />
    <circle cx="30" cy="30" r="15" fill="#4CAF50" />
    <circle cx="30" cy="30" r="8" fill="#FFFFFF" />
    
    <text x="30" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      专注制作
    </text>
  </g>
</svg>
