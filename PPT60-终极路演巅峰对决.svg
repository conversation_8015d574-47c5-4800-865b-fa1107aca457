<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64px" font-weight="bold" fill="#005A9E">
    "终极路演"巅峰对决
  </text>
  <text x="960" y="170" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" fill="#FF6B6B" font-weight="bold">
    Ultimate Pitch Competition
  </text>
  
  <!-- 任务说明 -->
  <g transform="translate(200, 220)">
    <rect x="0" y="0" width="1520" height="120" fill="#FF6B6B" rx="15" opacity="0.1" />
    <text x="760" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FF6B6B">
      任务 (60分钟)
    </text>
    
    <g transform="translate(100, 60)">
      <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
        • 将下午的《增长药方画布》，转化为5页PPT
      </text>
      <text x="800" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
        • 推选一位"主演"，进行5分钟路演
      </text>
    </g>
  </g>
  
  <!-- 听众和目标 -->
  <g transform="translate(200, 380)">
    <rect x="0" y="0" width="1520" height="120" fill="#FFD700" rx="15" opacity="0.1" />
    
    <g transform="translate(200, 30)">
      <text x="0" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#FF9800" font-weight="bold">
        听众：
      </text>
      <text x="100" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#333333">
        全场最挑剔的"投资人"
      </text>
      
      <text x="600" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#4CAF50" font-weight="bold">
        目标：
      </text>
      <text x="700" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#333333">
        赢得投资！
      </text>
    </g>
  </g>
  
  <!-- 竞技场景 -->
  <g transform="translate(200, 540)">
    <!-- 中央舞台 -->
    <rect x="500" y="0" width="520" height="300" fill="#FFD700" rx="15" opacity="0.2" />
    
    <!-- 舞台 -->
    <rect x="600" y="200" width="320" height="80" fill="#8B4513" rx="10" />
    <rect x="620" y="180" width="280" height="30" fill="#D2B48C" rx="5" />
    
    <!-- 聚光灯效果 -->
    <ellipse cx="760" cy="190" rx="120" ry="40" fill="#FFD700" opacity="0.4" />
    
    <!-- 当前演讲者 -->
    <circle cx="760" cy="150" r="25" fill="#4CAF50" />
    <rect x="745" y="175" width="30" height="40" fill="#005A9E" rx="6" />
    
    <!-- 演讲手势 -->
    <line x1="730" y1="185" x2="710" y2="170" stroke="#4CAF50" stroke-width="3" />
    <line x1="790" y1="185" x2="810" y2="170" stroke="#4CAF50" stroke-width="3" />
    
    <!-- 演讲内容 -->
    <ellipse cx="650" cy="120" rx="80" ry="30" fill="#FFFFFF" stroke="#4CAF50" stroke-width="2" />
    <text x="650" y="115" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333" font-weight="bold">
      "这个方案将为客户带来"
    </text>
    <text x="650" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      "180%的投资回报率！"
    </text>
    
    <text x="760" y="300" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333" font-weight="bold">
      巅峰对决进行中
    </text>
  </g>
  
  <!-- 左侧：小组A -->
  <g transform="translate(50, 540)">
    <rect x="0" y="0" width="400" height="300" fill="#4CAF50" rx="15" opacity="0.1" />
    <text x="200" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#4CAF50">
      小组A - 制造业方案
    </text>
    
    <!-- 团队成员 -->
    <g transform="translate(50, 50)">
      <!-- 主演 -->
      <circle cx="50" cy="50" r="25" fill="#4CAF50" />
      <rect x="35" y="75" width="30" height="40" fill="#005A9E" rx="6" />
      
      <!-- 王冠标识 -->
      <path d="M 35 35 L 45 25 L 55 35 L 65 25 L 75 35 L 65 45 L 35 45 Z" fill="#FFD700" />
      
      <text x="50" y="135" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">
        主演
      </text>
      
      <!-- 助手们 -->
      <circle cx="150" cy="70" r="20" fill="#2196F3" />
      <rect x="140" y="90" width="20" height="30" fill="#333333" rx="4" />
      
      <circle cx="220" cy="70" r="20" fill="#FF9800" />
      <rect x="210" y="90" width="20" height="30" fill="#005A9E" rx="4" />
      
      <circle cx="290" cy="70" r="20" fill="#9C27B0" />
      <rect x="280" y="90" width="20" height="30" fill="#333333" rx="4" />
      
      <text x="220" y="140" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#666666">
        支持团队
      </text>
    </g>
    
    <!-- PPT预览 -->
    <g transform="translate(50, 180)">
      <rect x="0" y="0" width="300" height="80" fill="#FFFFFF" stroke="#4CAF50" stroke-width="2" rx="5" />
      <text x="150" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#4CAF50" font-weight="bold">
        智能制造转型方案
      </text>
      <text x="150" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
        • 效率提升30% • 成本降低20%
      </text>
      <text x="150" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
        • ROI 180% • 18个月回本
      </text>
      <text x="150" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#666666">
        5页PPT完成度：100%
      </text>
    </g>
  </g>
  
  <!-- 右侧：小组B -->
  <g transform="translate(1270, 540)">
    <rect x="0" y="0" width="400" height="300" fill="#2196F3" rx="15" opacity="0.1" />
    <text x="200" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#2196F3">
      小组B - 金融服务方案
    </text>
    
    <!-- 团队成员 -->
    <g transform="translate(50, 50)">
      <!-- 主演 -->
      <circle cx="50" cy="50" r="25" fill="#2196F3" />
      <rect x="35" y="75" width="30" height="40" fill="#005A9E" rx="6" />
      
      <!-- 准备状态 -->
      <circle cx="75" cy="35" r="8" fill="#FF9800" />
      <text x="75" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8px" fill="#FFFFFF" font-weight="bold">
        待命
      </text>
      
      <text x="50" y="135" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">
        主演
      </text>
      
      <!-- 助手们 -->
      <circle cx="150" cy="70" r="20" fill="#4CAF50" />
      <rect x="140" y="90" width="20" height="30" fill="#333333" rx="4" />
      
      <circle cx="220" cy="70" r="20" fill="#FF9800" />
      <rect x="210" y="90" width="20" height="30" fill="#005A9E" rx="4" />
      
      <circle cx="290" cy="70" r="20" fill="#9C27B0" />
      <rect x="280" y="90" width="20" height="30" fill="#333333" rx="4" />
      
      <text x="220" y="140" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#666666">
        支持团队
      </text>
    </g>
    
    <!-- PPT预览 -->
    <g transform="translate(50, 180)">
      <rect x="0" y="0" width="300" height="80" fill="#FFFFFF" stroke="#2196F3" stroke-width="2" rx="5" />
      <text x="150" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#2196F3" font-weight="bold">
        数字化风控平台
      </text>
      <text x="150" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
        • 风险降低50% • 合规100%
      </text>
      <text x="150" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
        • ROI 220% • 12个月回本
      </text>
      <text x="150" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#666666">
        5页PPT完成度：100%
      </text>
    </g>
  </g>
  
  <!-- 底部激励 -->
  <rect x="200" y="880" width="1520" height="120" fill="#FF6B6B" rx="15" />
  <text x="960" y="920" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    大幕即将拉开！各"摄制组"准备好了吗？
  </text>
  <text x="960" y="960" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF">
    你们的听众，是全场最挑剔的"投资人"！
  </text>
  
  <!-- 奖杯图标 -->
  <g transform="translate(100, 540)">
    <!-- 奖杯 -->
    <ellipse cx="40" cy="60" rx="25" ry="15" fill="#FFD700" />
    <rect x="30" y="45" width="20" height="30" fill="#FFD700" rx="5" />
    <ellipse cx="40" cy="45" rx="15" ry="8" fill="#FFF8DC" />
    
    <!-- 奖杯柄 -->
    <rect x="15" y="50" width="8" height="15" fill="#FFD700" rx="4" />
    <rect x="57" y="50" width="8" height="15" fill="#FFD700" rx="4" />
    
    <!-- 底座 -->
    <rect x="25" y="75" width="30" height="8" fill="#8B4513" rx="4" />
    
    <text x="40" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      最佳方案
    </text>
  </g>
  
  <!-- 计时器 -->
  <g transform="translate(1700, 540)">
    <circle cx="60" cy="60" r="50" stroke="#FF6B6B" stroke-width="6" fill="#FFFFFF" />
    <circle cx="60" cy="60" r="5" fill="#FF6B6B" />
    
    <!-- 倒计时显示 -->
    <text x="60" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FF6B6B" font-weight="bold">
      60:00
    </text>
    <text x="60" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      总时长
    </text>
    
    <!-- 动态指针 -->
    <line x1="60" y1="60" x2="60" y2="25" stroke="#FF6B6B" stroke-width="3" />
    <line x1="60" y1="60" x2="85" y2="40" stroke="#005A9E" stroke-width="2" />
  </g>
</svg>
