<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="70px" font-weight="bold" fill="#005A9E">
    "解剖麻雀"工作坊
  </text>
  
  <!-- 任务 -->
  <g transform="translate(200, 250)">
    <text x="0" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40px" font-weight="bold" fill="#F5A623">
      任务：
    </text>
    <text x="120" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" fill="#333333">
      "解剖"【本地化案例：XX有限公司】
    </text>
  </g>
  
  <!-- 时间 -->
  <g transform="translate(200, 320)">
    <text x="0" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40px" font-weight="bold" fill="#F5A623">
      时间：
    </text>
    <text x="120" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" fill="#333333">
      60分钟
    </text>
  </g>
  
  <!-- 流程 -->
  <g transform="translate(200, 390)">
    <text x="0" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40px" font-weight="bold" fill="#F5A623">
      流程：
    </text>
    
    <text x="50" y="100" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" fill="#333333">
      1. 阅读案例资料包
    </text>
    <text x="50" y="150" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" fill="#333333">
      2. 小组研讨，完成《客户情报作战地图》
    </text>
    <text x="50" y="200" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" fill="#333333">
      3. 将成果呈现在A1大白纸上
    </text>
    <text x="50" y="250" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" fill="#333333">
      4. 准备3分钟成果汇报
    </text>
  </g>
  
  <!-- 目标 -->
  <g transform="translate(200, 700)">
    <text x="0" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40px" font-weight="bold" fill="#F5A623">
      目标：
    </text>
    <text x="120" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" fill="#333333">
      产出一份高质量的"洞察项链"
    </text>
  </g>
  
  <!-- 解剖图标 -->
  <g transform="translate(1200, 350)">
    <!-- 放大镜 -->
    <circle cx="100" cy="100" r="60" stroke="#005A9E" stroke-width="6" fill="none" />
    <circle cx="100" cy="100" r="45" stroke="#005A9E" stroke-width="2" fill="none" opacity="0.3" />
    <line x1="145" y1="145" x2="180" y2="180" stroke="#005A9E" stroke-width="8" />
    
    <!-- 被观察的对象 -->
    <rect x="70" y="70" width="60" height="60" fill="#F5A623" rx="5" opacity="0.6" />
    <text x="100" y="105" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FFFFFF" font-weight="bold">
      案例
    </text>
    
    <!-- 分析线条 -->
    <line x1="50" y1="50" x2="150" y2="150" stroke="#FF6B6B" stroke-width="2" stroke-dasharray="5,5" />
    <line x1="150" y1="50" x2="50" y2="150" stroke="#FF6B6B" stroke-width="2" stroke-dasharray="5,5" />
    
    <text x="100" y="220" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
      深度解剖
    </text>
  </g>
  
  <!-- 密封信封图标 -->
  <g transform="translate(1400, 450)">
    <rect x="0" y="0" width="120" height="80" fill="#F5A623" rx="5" />
    <polygon points="0,0 60,40 120,0" fill="#FFD700" />
    <circle cx="100" cy="20" r="8" fill="#FF6B6B" />
    <text x="100" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8px" fill="#FFFFFF" font-weight="bold">
      密封
    </text>
    
    <text x="60" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      资料包
    </text>
  </g>
  
  <!-- A1大白纸图标 -->
  <g transform="translate(1000, 600)">
    <rect x="0" y="0" width="150" height="100" fill="#FFFFFF" stroke="#005A9E" stroke-width="3" rx="5" />
    <text x="75" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#005A9E" font-weight="bold">
      作战地图
    </text>
    
    <!-- 四象限示意 -->
    <line x1="75" y1="10" x2="75" y2="90" stroke="#CCCCCC" stroke-width="1" />
    <line x1="10" y1="50" x2="140" y2="50" stroke="#CCCCCC" stroke-width="1" />
    
    <text x="75" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      A1大白纸
    </text>
  </g>
  
  <!-- 时钟图标 -->
  <g transform="translate(1500, 250)">
    <circle cx="50" cy="50" r="40" stroke="#F5A623" stroke-width="4" fill="#FFFFFF" />
    <circle cx="50" cy="50" r="3" fill="#F5A623" />
    <!-- 时针指向12点 -->
    <line x1="50" y1="50" x2="50" y2="25" stroke="#F5A623" stroke-width="3" />
    <!-- 分针指向12点 -->
    <line x1="50" y1="50" x2="50" y2="20" stroke="#005A9E" stroke-width="2" />
    
    <text x="50" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" font-weight="bold" fill="#F5A623">
      60分钟
    </text>
  </g>
  
  <!-- 底部激励语 -->
  <rect x="200" y="800" width="1520" height="100" fill="#005A9E" rx="15" />
  <text x="960" y="840" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FFFFFF">
    理论讲完了，现在是时候检验我们学习成果了！
  </text>
  <text x="960" y="880" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    60分钟，现在开始！
  </text>
</svg>
