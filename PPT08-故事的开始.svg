<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="nightGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#16213e;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  
  <rect width="100%" height="100%" fill="#FFFFFF" />
  <rect width="100%" height="100%" fill="url(#nightGradient)" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="300" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="62px" font-weight="bold" fill="#005A9E">
    故事的开始：一位销售冠军的"身份危机"
  </text>
  
  <!-- 城市夜景轮廓 -->
  <g transform="translate(200, 600)">
    <!-- 建筑物轮廓 -->
    <rect x="0" y="100" width="80" height="200" fill="#333333" opacity="0.7" />
    <rect x="100" y="50" width="60" height="250" fill="#333333" opacity="0.7" />
    <rect x="180" y="80" width="90" height="220" fill="#333333" opacity="0.7" />
    <rect x="290" y="40" width="70" height="260" fill="#333333" opacity="0.7" />
    <rect x="380" y="90" width="85" height="210" fill="#333333" opacity="0.7" />
    <rect x="480" y="60" width="75" height="240" fill="#333333" opacity="0.7" />
    <rect x="570" y="110" width="65" height="190" fill="#333333" opacity="0.7" />
    <rect x="650" y="70" width="80" height="230" fill="#333333" opacity="0.7" />
    <rect x="750" y="90" width="70" height="210" fill="#333333" opacity="0.7" />
    <rect x="840" y="50" width="90" height="250" fill="#333333" opacity="0.7" />
    <rect x="950" y="80" width="75" height="220" fill="#333333" opacity="0.7" />
    <rect x="1040" y="100" width="80" height="200" fill="#333333" opacity="0.7" />
    <rect x="1140" y="60" width="85" height="240" fill="#333333" opacity="0.7" />
    <rect x="1240" y="90" width="70" height="210" fill="#333333" opacity="0.7" />
    <rect x="1330" y="70" width="75" height="230" fill="#333333" opacity="0.7" />
    <rect x="1420" y="100" width="80" height="200" fill="#333333" opacity="0.7" />
    
    <!-- 窗户灯光 -->
    <rect x="10" y="120" width="8" height="8" fill="#F5A623" opacity="0.8" />
    <rect x="25" y="140" width="8" height="8" fill="#F5A623" opacity="0.8" />
    <rect x="40" y="160" width="8" height="8" fill="#F5A623" opacity="0.8" />
    <rect x="110" y="80" width="8" height="8" fill="#F5A623" opacity="0.8" />
    <rect x="125" y="100" width="8" height="8" fill="#F5A623" opacity="0.8" />
    <rect x="190" y="110" width="8" height="8" fill="#F5A623" opacity="0.8" />
    <rect x="205" y="130" width="8" height="8" fill="#F5A623" opacity="0.8" />
    <rect x="300" y="70" width="8" height="8" fill="#F5A623" opacity="0.8" />
    <rect x="315" y="90" width="8" height="8" fill="#F5A623" opacity="0.8" />
  </g>
  
  <!-- 商务人士背影 -->
  <g transform="translate(1300, 450)">
    <!-- 头部 -->
    <ellipse cx="50" cy="30" rx="25" ry="30" fill="#333333" />
    <!-- 身体 -->
    <rect x="20" y="60" width="60" height="120" fill="#333333" rx="10" />
    <!-- 手臂 -->
    <rect x="5" y="70" width="20" height="80" fill="#333333" rx="5" />
    <rect x="75" y="70" width="20" height="80" fill="#333333" rx="5" />
    <!-- 腿部 -->
    <rect x="30" y="180" width="18" height="60" fill="#333333" rx="5" />
    <rect x="52" y="180" width="18" height="60" fill="#333333" rx="5" />
  </g>
  
  <!-- 思考气泡 -->
  <g transform="translate(1100, 350)">
    <ellipse cx="80" cy="40" rx="70" ry="35" fill="#FFFFFF" stroke="#333333" stroke-width="2" opacity="0.9" />
    <circle cx="50" cy="70" r="8" fill="#FFFFFF" stroke="#333333" stroke-width="2" opacity="0.9" />
    <circle cx="35" cy="85" r="5" fill="#FFFFFF" stroke="#333333" stroke-width="2" opacity="0.9" />
    <text x="80" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      身份危机？
    </text>
  </g>
</svg>
