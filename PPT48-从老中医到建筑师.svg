<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="70px" font-weight="bold" fill="#005A9E">
    从"老中医"到"建筑师"
  </text>
  
  <!-- 上午回顾 -->
  <g transform="translate(200, 280)">
    <text x="0" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" fill="#333333">
      上午，我们学会了"望闻问切"，精准号脉。
    </text>
    
    <!-- 老中医剪影 -->
    <g transform="translate(100, 80)">
      <ellipse cx="100" cy="150" rx="80" ry="120" fill="#4CAF50" opacity="0.3" />
      
      <!-- 老中医人物 -->
      <circle cx="100" cy="80" r="35" fill="#4CAF50" />
      <rect x="75" y="115" width="50" height="80" fill="#8B4513" rx="8" />
      
      <!-- 胡须 -->
      <path d="M 85 95 Q 90 105 85 110" stroke="#FFFFFF" stroke-width="3" fill="none" />
      <path d="M 100 100 Q 105 110 100 115" stroke="#FFFFFF" stroke-width="3" fill="none" />
      <path d="M 115 95 Q 110 105 115 110" stroke="#FFFFFF" stroke-width="3" fill="none" />
      
      <!-- 面部特征 -->
      <circle cx="90" cy="75" r="3" fill="#FFFFFF" />
      <circle cx="110" cy="75" r="3" fill="#FFFFFF" />
      
      <!-- 号脉手势 -->
      <ellipse cx="160" cy="130" rx="15" ry="8" fill="#F5A623" />
      <line x1="125" y1="140" x2="145" y2="130" stroke="#4CAF50" stroke-width="4" />
      
      <!-- 脉搏波形 -->
      <path d="M 180 130 Q 190 120 200 130 Q 210 140 220 130" stroke="#FF6B6B" stroke-width="2" fill="none" />
      
      <text x="100" y="280" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#4CAF50" font-weight="bold">
        老中医号脉
      </text>
    </g>
  </g>
  
  <!-- 下午预告 -->
  <g transform="translate(200, 600)">
    <text x="0" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" fill="#333333">
      下午，我们学习"擘画蓝图"，开出良方。
    </text>
    
    <!-- 建筑师剪影 -->
    <g transform="translate(1200, 80)">
      <ellipse cx="100" cy="150" rx="80" ry="120" fill="#2196F3" opacity="0.3" />
      
      <!-- 建筑师人物 -->
      <circle cx="100" cy="80" r="35" fill="#2196F3" />
      <rect x="75" y="115" width="50" height="80" fill="#333333" rx="8" />
      
      <!-- 安全帽 -->
      <ellipse cx="100" cy="65" rx="30" ry="15" fill="#FFD700" />
      <rect x="85" y="60" width="30" height="8" fill="#FFD700" />
      
      <!-- 面部特征 -->
      <circle cx="90" cy="75" r="3" fill="#FFFFFF" />
      <circle cx="110" cy="75" r="3" fill="#FFFFFF" />
      <path d="M 90 85 Q 100 90 110 85" stroke="#FFFFFF" stroke-width="2" fill="none" />
      
      <!-- 蓝图 -->
      <rect x="130" y="120" width="40" height="30" fill="#FFFFFF" stroke="#2196F3" stroke-width="2" rx="3" />
      <line x1="135" y1="128" x2="165" y2="128" stroke="#2196F3" stroke-width="1" />
      <line x1="135" y1="135" x2="155" y2="135" stroke="#2196F3" stroke-width="1" />
      <line x1="135" y1="142" x2="160" y2="142" stroke="#2196F3" stroke-width="1" />
      
      <!-- 绘图工具 -->
      <line x1="125" y1="140" x2="130" y2="125" stroke="#F5A623" stroke-width="3" />
      <circle cx="128" cy="122" r="2" fill="#F5A623" />
      
      <text x="100" y="280" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#2196F3" font-weight="bold">
        建筑师绘图
      </text>
    </g>
  </g>
  
  <!-- 模块四标识 -->
  <g transform="translate(200, 800)">
    <rect x="0" y="0" width="1520" height="100" fill="#F5A623" rx="15" />
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#FFFFFF">
      模块四：妙手开方
    </text>
    <text x="760" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#FFFFFF">
      从诊断到开方的完美转换
    </text>
  </g>
  
  <!-- 转换箭头 -->
  <g transform="translate(700, 450)">
    <path d="M 0 0 L 120 0" stroke="#F5A623" stroke-width="8" marker-end="url(#arrowhead)" />
    <text x="60" y="-15" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#F5A623" font-weight="bold">
      转换
    </text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="15" markerHeight="10" refX="14" refY="5" orient="auto">
      <polygon points="0 0, 15 5, 0 10" fill="#F5A623" />
    </marker>
  </defs>
  
  <!-- 比喻说明 -->
  <g transform="translate(200, 950)">
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#005A9E">
      但诊断之后，如果只是从药架上随便抓几味药，那最多算个"抓药的"
    </text>
    <text x="760" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#005A9E">
      而真正的大夫，懂得"君臣佐使，配伍严谨"
    </text>
  </g>
  
  <!-- 药材图标 -->
  <g transform="translate(100, 800)">
    <!-- 药材罐 -->
    <rect x="0" y="20" width="30" height="40" fill="#8B4513" rx="5" />
    <ellipse cx="15" cy="20" rx="15" ry="5" fill="#A0522D" />
    <rect x="5" y="25" width="20" height="3" fill="#654321" />
    
    <rect x="40" y="15" width="25" height="35" fill="#8B4513" rx="4" />
    <ellipse cx="52.5" cy="15" rx="12.5" ry="4" fill="#A0522D" />
    
    <rect x="75" y="25" width="20" height="30" fill="#8B4513" rx="3" />
    <ellipse cx="85" cy="25" rx="10" ry="3" fill="#A0522D" />
    
    <text x="50" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      药材配伍
    </text>
  </g>
  
  <!-- 蓝图图标 -->
  <g transform="translate(1700, 800)">
    <!-- 蓝图卷轴 -->
    <rect x="0" y="20" width="60" height="40" fill="#E6F3FF" stroke="#2196F3" stroke-width="2" rx="5" />
    <line x1="10" y1="30" x2="50" y2="30" stroke="#2196F3" stroke-width="1" />
    <line x1="10" y1="38" x2="35" y2="38" stroke="#2196F3" stroke-width="1" />
    <line x1="10" y1="46" x2="45" y2="46" stroke="#2196F3" stroke-width="1" />
    <line x1="10" y1="54" x2="40" y2="54" stroke="#2196F3" stroke-width="1" />
    
    <!-- 卷轴边缘 -->
    <circle cx="0" cy="40" r="8" fill="#D0D0D0" />
    <circle cx="60" cy="40" r="8" fill="#D0D0D0" />
    
    <text x="30" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      建筑蓝图
    </text>
  </g>
</svg>
