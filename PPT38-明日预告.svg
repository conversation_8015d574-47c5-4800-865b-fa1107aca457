<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="70px" font-weight="bold" fill="#005A9E">
    明日预告
  </text>
  
  <!-- 第二天课程安排 -->
  <g transform="translate(200, 280)">
    <text x="0" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="44px" font-weight="bold" fill="#F5A623">
      第二天：技能淬炼与实战通关
    </text>
    
    <!-- 上午模块 -->
    <g transform="translate(100, 100)">
      <rect x="0" y="0" width="500" height="80" fill="#4CAF50" rx="15" opacity="0.1" />
      <text x="20" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#4CAF50">
        上午：
      </text>
      <text x="100" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
        模块三 - 场景深潜 (SPIN诊断式提问)
      </text>
      
      <!-- 听诊器图标 -->
      <g transform="translate(420, 20)">
        <circle cx="20" cy="20" r="15" stroke="#4CAF50" stroke-width="3" fill="none" />
        <path d="M 35 20 Q 45 10 55 20 Q 45 30 35 20" stroke="#4CAF50" stroke-width="2" fill="none" />
        <path d="M 35 20 Q 45 30 55 20 Q 45 10 35 20" stroke="#4CAF50" stroke-width="2" fill="none" />
      </g>
    </g>
    
    <!-- 下午模块 -->
    <g transform="translate(100, 200)">
      <rect x="0" y="0" width="500" height="80" fill="#2196F3" rx="15" opacity="0.1" />
      <text x="20" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#2196F3">
        下午：
      </text>
      <text x="100" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
        模块四 - 妙手开方 (方案架构设计)
      </text>
      
      <!-- 蓝图图标 -->
      <g transform="translate(420, 20)">
        <rect x="0" y="0" width="40" height="30" fill="none" stroke="#2196F3" stroke-width="2" rx="3" />
        <line x1="5" y1="8" x2="35" y2="8" stroke="#2196F3" stroke-width="1" />
        <line x1="5" y1="15" x2="25" y2="15" stroke="#2196F3" stroke-width="1" />
        <line x1="5" y1="22" x2="30" y2="22" stroke="#2196F3" stroke-width="1" />
      </g>
    </g>
    
    <!-- 晚间模块 -->
    <g transform="translate(100, 300)">
      <rect x="0" y="0" width="500" height="80" fill="#9C27B0" rx="15" opacity="0.1" />
      <text x="20" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#9C27B0">
        晚间：
      </text>
      <text x="100" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
        模块五 - 价值呈现 (终极路演对决)
      </text>
      
      <!-- 舞台图标 -->
      <g transform="translate(420, 20)">
        <rect x="0" y="25" width="40" height="15" fill="#9C27B0" rx="2" />
        <path d="M 5 25 L 20 10 L 35 25" stroke="#9C27B0" stroke-width="2" fill="none" />
        <circle cx="20" cy="5" r="3" fill="#FFD700" />
      </g>
    </g>
  </g>
  
  <!-- 登山挑战图示 -->
  <g transform="translate(1000, 300)">
    <!-- 山峰 -->
    <path d="M 100 400 L 200 200 L 300 250 L 400 150 L 500 300 L 600 400" 
          stroke="#8B4513" stroke-width="4" fill="none" />
    <path d="M 100 400 L 200 200 L 300 250 L 400 150 L 500 300 L 600 400 L 600 450 L 100 450 Z" 
          fill="#D2B48C" opacity="0.6" />
    
    <!-- 山峰积雪 -->
    <path d="M 180 220 L 200 200 L 220 220 L 200 230 Z" fill="#FFFFFF" />
    <path d="M 380 170 L 400 150 L 420 170 L 400 180 Z" fill="#FFFFFF" />
    
    <!-- 登山者 -->
    <g transform="translate(150, 350)">
      <circle cx="10" cy="10" r="8" fill="#F5A623" />
      <rect x="6" y="18" width="8" height="20" fill="#005A9E" rx="2" />
      <line x1="10" y1="25" x2="20" y2="20" stroke="#F5A623" stroke-width="2" />
      <!-- 登山杖 -->
      <line x1="20" y1="20" x2="25" y2="35" stroke="#8B4513" stroke-width="2" />
    </g>
    
    <!-- 攀登路径 -->
    <path d="M 160 360 Q 250 300 350 280 Q 450 200 500 180" 
          stroke="#F5A623" stroke-width="3" stroke-dasharray="5,5" opacity="0.8" />
    
    <!-- 旗帜 -->
    <g transform="translate(480, 150)">
      <line x1="0" y1="0" x2="0" y2="40" stroke="#333333" stroke-width="2" />
      <polygon points="0,0 25,8 0,16" fill="#FF6B6B" />
    </g>
    
    <text x="350" y="500" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333" font-weight="bold">
      充满挑战与希望的征程
    </text>
  </g>
  
  <!-- 技能进阶路径 -->
  <g transform="translate(200, 700)">
    <rect x="0" y="0" width="1520" height="120" fill="#005A9E" rx="15" opacity="0.1" />
    
    <!-- 技能点1 -->
    <g transform="translate(100, 30)">
      <circle cx="30" cy="30" r="25" fill="#4CAF50" />
      <text x="30" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">
        诊断
      </text>
      <text x="30" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        SPIN提问
      </text>
    </g>
    
    <!-- 箭头 -->
    <path d="M 180 60 L 220 60" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
    
    <!-- 技能点2 -->
    <g transform="translate(250, 30)">
      <circle cx="30" cy="30" r="25" fill="#2196F3" />
      <text x="30" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">
        开方
      </text>
      <text x="30" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        方案设计
      </text>
    </g>
    
    <!-- 箭头 -->
    <path d="M 330 60 L 370 60" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
    
    <!-- 技能点3 -->
    <g transform="translate(400, 30)">
      <circle cx="30" cy="30" r="25" fill="#9C27B0" />
      <text x="30" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">
        呈现
      </text>
      <text x="30" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        价值路演
      </text>
    </g>
    
    <!-- 箭头 -->
    <path d="M 480 60 L 520 60" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
    
    <!-- 最终目标 -->
    <g transform="translate(550, 30)">
      <circle cx="30" cy="30" r="25" fill="#FFD700" />
      <text x="30" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333" font-weight="bold">
        顾问
      </text>
      <text x="30" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        完美蜕变
      </text>
    </g>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#F5A623" />
    </marker>
  </defs>
  
  <!-- 底部激励 -->
  <rect x="200" y="850" width="1520" height="80" fill="#F5A623" rx="15" />
  <text x="960" y="900" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#FFFFFF">
    养精蓄锐，明日再战！
  </text>
  
  <!-- 星星装饰 -->
  <g transform="translate(1600, 250)">
    <path d="M 20 5 L 25 15 L 35 15 L 27 22 L 30 32 L 20 27 L 10 32 L 13 22 L 5 15 L 15 15 Z" fill="#FFD700" />
  </g>
  
  <g transform="translate(100, 500)">
    <path d="M 15 3 L 18 12 L 27 12 L 20 17 L 23 26 L 15 21 L 7 26 L 10 17 L 3 12 L 12 12 Z" fill="#4A90E2" />
  </g>
</svg>
