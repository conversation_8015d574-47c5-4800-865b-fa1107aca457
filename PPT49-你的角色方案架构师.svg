<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="70px" font-weight="bold" fill="#005A9E">
    你的角色：方案架构师
  </text>
  
  <!-- 左右对比 -->
  <g transform="translate(200, 250)">
    <!-- 产品配置员 -->
    <g transform="translate(0, 0)">
      <rect x="0" y="0" width="650" height="400" fill="#FF6B6B" rx="15" opacity="0.1" />
      <text x="325" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#FF6B6B">
        产品配置员
      </text>
      <text x="325" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#666666">
        (Product Configurator)
      </text>
      
      <!-- 产品配置员图标 -->
      <g transform="translate(200, 120)">
        <circle cx="50" cy="50" r="40" fill="#FF6B6B" />
        <rect x="30" y="90" width="40" height="60" fill="#333333" rx="8" />
        
        <!-- 面部特征 -->
        <circle cx="40" cy="45" r="4" fill="#FFFFFF" />
        <circle cx="60" cy="45" r="4" fill="#FFFFFF" />
        <path d="M 40 60 Q 50 55 60 60" stroke="#FFFFFF" stroke-width="2" fill="none" />
        
        <!-- 产品清单 -->
        <rect x="100" y="120" width="50" height="70" fill="#FFFFFF" stroke="#FF6B6B" stroke-width="2" rx="5" />
        <line x1="110" y1="130" x2="140" y2="130" stroke="#FF6B6B" stroke-width="2" />
        <line x1="110" y1="140" x2="135" y2="140" stroke="#FF6B6B" stroke-width="2" />
        <line x1="110" y1="150" x2="140" y2="150" stroke="#FF6B6B" stroke-width="2" />
        <line x1="110" y1="160" x2="130" y2="160" stroke="#FF6B6B" stroke-width="2" />
        <line x1="110" y1="170" x2="138" y2="170" stroke="#FF6B6B" stroke-width="2" />
        <line x1="110" y1="180" x2="135" y2="180" stroke="#FF6B6B" stroke-width="2" />
        
        <text x="125" y="210" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
          产品清单
        </text>
      </g>
      
      <!-- 特征描述 -->
      <g transform="translate(50, 280)">
        <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#FF6B6B">
          思维：
        </text>
        <text x="80" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" fill="#333333">
          由内向外
        </text>
        
        <text x="0" y="60" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#FF6B6B">
          语言：
        </text>
        <text x="80" y="60" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" fill="#333333">
          "我有什么可以卖"
        </text>
        
        <text x="0" y="95" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#FF6B6B">
          交付：
        </text>
        <text x="80" y="95" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" fill="#333333">
          产品清单
        </text>
      </g>
    </g>
    
    <!-- VS -->
    <text x="760" y="200" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48px" font-weight="bold" fill="#F5A623">
      VS
    </text>
    
    <!-- 方案架构师 -->
    <g transform="translate(870, 0)">
      <rect x="0" y="0" width="650" height="400" fill="#4CAF50" rx="15" opacity="0.1" />
      <text x="325" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#4CAF50">
        方案架构师
      </text>
      <text x="325" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#666666">
        (Solution Architect)
      </text>
      
      <!-- 方案架构师图标 -->
      <g transform="translate(200, 120)">
        <circle cx="50" cy="50" r="40" fill="#4CAF50" />
        <rect x="30" y="90" width="40" height="60" fill="#005A9E" rx="8" />
        
        <!-- 面部特征 -->
        <circle cx="40" cy="45" r="4" fill="#FFFFFF" />
        <circle cx="60" cy="45" r="4" fill="#FFFFFF" />
        <path d="M 40 60 Q 50 65 60 60" stroke="#FFFFFF" stroke-width="2" fill="none" />
        
        <!-- 业务蓝图 -->
        <rect x="100" y="120" width="50" height="70" fill="#E6F3FF" stroke="#4CAF50" stroke-width="2" rx="5" />
        
        <!-- 蓝图内容 - 流程图 -->
        <rect x="110" y="130" width="12" height="8" fill="#4CAF50" rx="2" />
        <rect x="130" y="130" width="12" height="8" fill="#2196F3" rx="2" />
        <line x1="122" y1="134" x2="130" y2="134" stroke="#4CAF50" stroke-width="2" />
        
        <rect x="110" y="150" width="12" height="8" fill="#FF9800" rx="2" />
        <rect x="130" y="150" width="12" height="8" fill="#9C27B0" rx="2" />
        <line x1="122" y1="154" x2="130" y2="154" stroke="#FF9800" stroke-width="2" />
        
        <!-- 连接线 -->
        <line x1="116" y1="138" x2="116" y2="150" stroke="#333333" stroke-width="1" />
        <line x1="136" y1="138" x2="136" y2="150" stroke="#333333" stroke-width="1" />
        
        <text x="125" y="210" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
          业务蓝图
        </text>
      </g>
      
      <!-- 特征描述 -->
      <g transform="translate(50, 280)">
        <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#4CAF50">
          思维：
        </text>
        <text x="80" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" fill="#333333">
          由外向内
        </text>
        
        <text x="0" y="60" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#4CAF50">
          语言：
        </text>
        <text x="80" y="60" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" fill="#333333">
          "客户的未来需要什么"
        </text>
        
        <text x="0" y="95" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#4CAF50">
          交付：
        </text>
        <text x="80" y="95" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" fill="#333333">
          业务蓝图
        </text>
      </g>
    </g>
  </g>
  
  <!-- 核心金句 -->
  <rect x="200" y="700" width="1520" height="120" fill="#005A9E" rx="15" />
  <text x="960" y="740" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    在开方阶段，你的身份不是"产品配置员"，而是"方案架构师"
  </text>
  <text x="960" y="780" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FFD700">
    记住，我们卖的不是一堆砖头和水泥，我们卖的是客户梦想中的那座"罗马城"的蓝图
  </text>
  
  <!-- 罗马城图标 -->
  <g transform="translate(100, 700)">
    <!-- 城堡轮廓 -->
    <rect x="10" y="40" width="60" height="40" fill="#D2B48C" rx="5" />
    <rect x="20" y="30" width="15" height="20" fill="#D2B48C" rx="2" />
    <rect x="45" y="25" width="15" height="25" fill="#D2B48C" rx="2" />
    <rect x="25" y="20" width="8" height="15" fill="#8B4513" rx="1" />
    <rect x="47" y="15" width="8" height="15" fill="#8B4513" rx="1" />
    
    <!-- 旗帜 -->
    <line x1="55" y1="15" x2="55" y2="5" stroke="#8B4513" stroke-width="2" />
    <polygon points="55,5 65,8 55,11" fill="#FF6B6B" />
    
    <text x="40" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      罗马城
    </text>
  </g>
  
  <!-- 砖头图标 -->
  <g transform="translate(1700, 700)">
    <!-- 散乱的砖头 -->
    <rect x="10" y="50" width="20" height="10" fill="#8B4513" rx="2" />
    <rect x="35" y="45" width="20" height="10" fill="#A0522D" rx="2" />
    <rect x="20" y="65" width="20" height="10" fill="#8B4513" rx="2" />
    <rect x="45" y="60" width="20" height="10" fill="#A0522D" rx="2" />
    
    <!-- 水泥袋 -->
    <rect x="5" y="30" width="15" height="25" fill="#CCCCCC" rx="3" />
    <rect x="8" y="25" width="9" height="8" fill="#999999" rx="2" />
    
    <text x="35" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      砖头水泥
    </text>
  </g>
</svg>
