<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="70px" font-weight="bold" fill="#005A9E">
    集体复盘与总结
  </text>
  
  <!-- 三个核心提问 -->
  <g transform="translate(200, 250)">
    <!-- 顾问的挑战 -->
    <g transform="translate(0, 0)">
      <rect x="0" y="0" width="450" height="120" fill="#4CAF50" rx="15" opacity="0.1" />
      <text x="225" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#4CAF50">
        "顾问"的最大挑战是什么？
      </text>
      
      <!-- 顾问图标 -->
      <g transform="translate(50, 50)">
        <circle cx="25" cy="25" r="20" fill="#4CAF50" />
        <rect x="15" y="45" width="20" height="30" fill="#005A9E" rx="3" />
        <circle cx="20" cy="20" r="2" fill="#FFFFFF" />
        <circle cx="30" cy="20" r="2" fill="#FFFFFF" />
        <path d="M 20 30 Q 25 35 30 30" stroke="#FFFFFF" stroke-width="2" fill="none" />
        
        <!-- 困难标识 -->
        <circle cx="50" cy="15" r="8" fill="#FF6B6B" />
        <text x="50" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8px" fill="#FFFFFF" font-weight="bold">!</text>
      </g>
      
      <text x="225" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#666666">
        I-暗示问题环节被忽略
      </text>
    </g>
    
    <!-- 客户印象 -->
    <g transform="translate(535, 0)">
      <rect x="0" y="0" width="450" height="120" fill="#2196F3" rx="15" opacity="0.1" />
      <text x="225" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#2196F3">
        "客户"印象最深的问题是哪个？
      </text>
      
      <!-- 客户图标 -->
      <g transform="translate(50, 50)">
        <circle cx="25" cy="25" r="20" fill="#2196F3" />
        <rect x="15" y="45" width="20" height="30" fill="#333333" rx="3" />
        <circle cx="20" cy="20" r="2" fill="#FFFFFF" />
        <circle cx="30" cy="20" r="2" fill="#FFFFFF" />
        <path d="M 20 30 Q 25 35 30 30" stroke="#FFFFFF" stroke-width="2" fill="none" />
        
        <!-- 印象深刻标识 -->
        <circle cx="50" cy="15" r="8" fill="#FFD700" />
        <text x="50" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8px" fill="#333333" font-weight="bold">★</text>
      </g>
      
      <text x="225" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#666666">
        哪个问题最有穿透力
      </text>
    </g>
    
    <!-- 观察员发现 -->
    <g transform="translate(1070, 0)">
      <rect x="0" y="0" width="450" height="120" fill="#FF9800" rx="15" opacity="0.1" />
      <text x="225" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#FF9800">
        "观察员"发现的共性问题是什么？
      </text>
      
      <!-- 观察员图标 -->
      <g transform="translate(50, 50)">
        <circle cx="25" cy="25" r="20" fill="#FF9800" />
        <rect x="15" y="45" width="20" height="30" fill="#005A9E" rx="3" />
        <circle cx="20" cy="20" r="2" fill="#FFFFFF" />
        <circle cx="30" cy="20" r="2" fill="#FFFFFF" />
        <path d="M 20 30 Q 25 35 30 30" stroke="#FFFFFF" stroke-width="2" fill="none" />
        
        <!-- 记录本 -->
        <rect x="45" y="40" width="12" height="16" fill="#FFFFFF" stroke="#FF9800" stroke-width="1" rx="1" />
        <line x1="47" y1="44" x2="55" y2="44" stroke="#FF9800" stroke-width="1" />
        <line x1="47" y1="48" x2="53" y2="48" stroke="#FF9800" stroke-width="1" />
        <line x1="47" y1="52" x2="55" y2="52" stroke="#FF9800" stroke-width="1" />
      </g>
      
      <text x="225" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#666666">
        客观记录的共性问题
      </text>
    </g>
  </g>
  
  <!-- 共性问题分析 -->
  <g transform="translate(200, 420)">
    <rect x="0" y="0" width="1520" height="200" fill="#FF6B6B" rx="15" opacity="0.1" />
    <text x="760" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FF6B6B">
      共性问题发现
    </text>
    
    <!-- 问题描述 -->
    <g transform="translate(100, 60)">
      <text x="0" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
        很多组的"顾问"，在好不容易问出一个P（难点）之后，
      </text>
      <text x="0" y="65" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
        就像挖到了金子一样，立刻就想把它装进口袋，
      </text>
      <text x="0" y="100" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
        急于跳到N（效益）去谈解决方案。
      </text>
      <text x="0" y="135" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FF6B6B">
        中间最关键的I（暗示）环节，被严重忽略了。
      </text>
    </g>
    
    <!-- 金子图标 -->
    <g transform="translate(1300, 80)">
      <circle cx="40" cy="40" r="30" fill="#FFD700" opacity="0.3" />
      <path d="M 25 25 L 35 35 L 55 35 L 45 25 L 55 15 L 35 15 Z" fill="#FFD700" />
      <text x="40" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        急于装袋
      </text>
    </g>
  </g>
  
  <!-- 医生比喻 -->
  <g transform="translate(200, 660)">
    <rect x="0" y="0" width="1520" height="120" fill="#9C27B0" rx="15" opacity="0.1" />
    <text x="760" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#9C27B0">
      医生比喻
    </text>
    
    <text x="760" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
      这就像医生问了你"哪里不舒服"，你说"头疼"，他立刻就给你开头疼药。
    </text>
    <text x="760" y="95" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
      他没有问你"这个头疼对你工作、生活有什么影响？影响有多大？"
    </text>
    
    <!-- 医生图标 -->
    <g transform="translate(100, 50)">
      <circle cx="25" cy="25" r="20" fill="#4CAF50" />
      <rect x="15" y="45" width="20" height="30" fill="#FFFFFF" rx="3" />
      <rect x="10" y="50" width="30" height="3" fill="#4CAF50" />
      
      <!-- 听诊器 -->
      <circle cx="25" cy="60" r="6" stroke="#333333" stroke-width="2" fill="none" />
      <path d="M 25 54 Q 18 48 12 54" stroke="#333333" stroke-width="1" fill="none" />
      <path d="M 25 54 Q 32 48 38 54" stroke="#333333" stroke-width="1" fill="none" />
    </g>
    
    <!-- 药瓶图标 -->
    <g transform="translate(1400, 50)">
      <rect x="0" y="15" width="20" height="35" fill="#FFFFFF" stroke="#9C27B0" stroke-width="2" rx="3" />
      <rect x="5" y="10" width="10" height="8" fill="#9C27B0" rx="2" />
      <circle cx="6" cy="25" r="2" fill="#FF6B6B" />
      <circle cx="14" cy="30" r="2" fill="#FF6B6B" />
      <circle cx="10" cy="40" r="2" fill="#FF6B6B" />
    </g>
  </g>
  
  <!-- 底部总结 -->
  <rect x="200" y="820" width="1520" height="120" fill="#005A9E" rx="15" />
  <text x="960" y="860" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    诊断的能力，唯有通过反复练习才能掌握
  </text>
  <text x="960" y="900" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#FFD700">
    虽然有些同学可能还呛了水，但这非常宝贵
  </text>
  
  <!-- 铃铛图标 -->
  <g transform="translate(100, 100)">
    <path d="M 30 20 Q 20 10 10 20 Q 10 30 20 35 Q 30 30 40 20 Q 40 10 30 20" fill="#FFD700" />
    <circle cx="25" cy="40" r="3" fill="#FFD700" />
    <line x1="25" y1="43" x2="25" y2="48" stroke="#333333" stroke-width="2" />
    
    <text x="25" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      时间到
    </text>
  </g>
</svg>
