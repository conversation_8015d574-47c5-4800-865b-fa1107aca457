<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="70px" font-weight="bold" fill="#005A9E">
    第二天：技能淬炼与实战通关
  </text>
  
  <!-- 核心金句 -->
  <g transform="translate(200, 250)">
    <rect x="0" y="0" width="1520" height="100" fill="#F5A623" rx="15" opacity="0.1" />
    
    <!-- 引号 -->
    <text x="50" y="60" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="60px" fill="#F5A623" opacity="0.5">"</text>
    
    <text x="760" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48px" font-weight="bold" fill="#F5A623">
      诊断，是让客户自己说服自己的艺术
    </text>
    
    <text x="1400" y="85" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="60px" fill="#F5A623" opacity="0.5">"</text>
  </g>
  
  <!-- 本日核心模块 -->
  <g transform="translate(200, 400)">
    <text x="0" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40px" font-weight="bold" fill="#005A9E">
      本日核心模块：
    </text>
    
    <!-- 模块三 -->
    <g transform="translate(100, 80)">
      <rect x="0" y="0" width="400" height="80" fill="#4CAF50" rx="15" opacity="0.1" />
      <text x="20" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#4CAF50">
        模块三：
      </text>
      <text x="120" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
        场景深潜 (SPIN)
      </text>
      <text x="20" y="60" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#666666">
        结构化诊断提问法
      </text>
    </g>
    
    <!-- 模块四 -->
    <g transform="translate(600, 80)">
      <rect x="0" y="0" width="400" height="80" fill="#2196F3" rx="15" opacity="0.1" />
      <text x="20" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#2196F3">
        模块四：
      </text>
      <text x="120" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
        妙手开方 (画布)
      </text>
      <text x="20" y="60" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#666666">
        方案架构设计
      </text>
    </g>
    
    <!-- 模块五 -->
    <g transform="translate(1100, 80)">
      <rect x="0" y="0" width="400" height="80" fill="#9C27B0" rx="15" opacity="0.1" />
      <text x="20" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#9C27B0">
        模块五：
      </text>
      <text x="120" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
        价值呈现 (路演)
      </text>
      <text x="20" y="60" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#666666">
        终极路演对决
      </text>
    </g>
  </g>
  
  <!-- 医生与建筑师合成图 -->
  <g transform="translate(1200, 600)">
    <!-- 医生形象 -->
    <g transform="translate(0, 0)">
      <circle cx="50" cy="40" r="30" fill="#4CAF50" />
      <rect x="35" y="70" width="30" height="50" fill="#FFFFFF" rx="5" />
      <rect x="30" y="75" width="40" height="5" fill="#4CAF50" />
      
      <!-- 面部特征 -->
      <circle cx="42" cy="35" r="3" fill="#FFFFFF" />
      <circle cx="58" cy="35" r="3" fill="#FFFFFF" />
      <path d="M 42 50 Q 50 55 58 50" stroke="#FFFFFF" stroke-width="2" fill="none" />
      
      <!-- 听诊器 -->
      <circle cx="50" cy="85" r="8" stroke="#333333" stroke-width="2" fill="none" />
      <path d="M 50 77 Q 40 70 30 77" stroke="#333333" stroke-width="2" fill="none" />
      <path d="M 50 77 Q 60 70 70 77" stroke="#333333" stroke-width="2" fill="none" />
      
      <text x="50" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#4CAF50" font-weight="bold">
        老中医
      </text>
      <text x="50" y="170" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        望闻问切
      </text>
    </g>
    
    <!-- 建筑师形象 -->
    <g transform="translate(200, 0)">
      <circle cx="50" cy="40" r="30" fill="#2196F3" />
      <rect x="35" y="70" width="30" height="50" fill="#333333" rx="5" />
      
      <!-- 面部特征 -->
      <circle cx="42" cy="35" r="3" fill="#FFFFFF" />
      <circle cx="58" cy="35" r="3" fill="#FFFFFF" />
      <path d="M 42 50 Q 50 55 58 50" stroke="#FFFFFF" stroke-width="2" fill="none" />
      
      <!-- 安全帽 -->
      <ellipse cx="50" cy="25" rx="25" ry="12" fill="#FFD700" />
      
      <!-- 蓝图 -->
      <rect x="70" y="80" width="25" height="20" fill="#FFFFFF" stroke="#2196F3" stroke-width="1" rx="2" />
      <line x1="73" y1="85" x2="92" y2="85" stroke="#2196F3" stroke-width="1" />
      <line x1="73" y1="90" x2="87" y2="90" stroke="#2196F3" stroke-width="1" />
      <line x1="73" y1="95" x2="90" y2="95" stroke="#2196F3" stroke-width="1" />
      
      <text x="50" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#2196F3" font-weight="bold">
        建筑师
      </text>
      <text x="50" y="170" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        擘画蓝图
      </text>
    </g>
  </g>
  
  <!-- 昨晚复盘回顾 -->
  <g transform="translate(200, 650)">
    <rect x="0" y="0" width="800" height="120" fill="#1A237E" rx="15" opacity="0.1" />
    <text x="400" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#1A237E">
      昨夜的痛，是为了让我们看清今日的光
    </text>
    
    <!-- 冰山小图标 -->
    <g transform="translate(50, 50)">
      <line x1="0" y1="30" x2="60" y2="30" stroke="#4A90E2" stroke-width="2" />
      <path d="M 20 20 L 40 20 L 45 30 L 15 30 Z" fill="#E6F3FF" stroke="#4A90E2" stroke-width="1" />
      <path d="M 15 30 L 45 30 L 55 50 L 5 50 Z" fill="#B3E5FC" stroke="#4A90E2" stroke-width="1" />
    </g>
    
    <text x="400" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
      几乎所有小组，都把失败的根源，指向了一个共同的原点：
    </text>
    <text x="400" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#FF6B6B">
      诊断失误
    </text>
  </g>
  
  <!-- 今日目标 -->
  <g transform="translate(200, 800)">
    <rect x="0" y="0" width="1520" height="120" fill="#005A9E" rx="15" />
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
      今天，我们将用一整天的时间，来学习并操练
    </text>
    <text x="760" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FFD700">
      "诊断"、"开方"和"呈现"的全套"组合拳"
    </text>
    <text x="760" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#FFFFFF">
      首先，学习拥有"透视眼"、看清冰山之下的结构化诊断工具
    </text>
  </g>
  
  <!-- 透视眼图标 -->
  <g transform="translate(100, 400)">
    <!-- 眼睛 -->
    <ellipse cx="50" cy="30" rx="40" ry="25" fill="#FFFFFF" stroke="#005A9E" stroke-width="3" />
    <circle cx="50" cy="30" r="15" fill="#005A9E" />
    <circle cx="50" cy="30" r="8" fill="#FFFFFF" />
    
    <!-- 透视光线 -->
    <path d="M 90 30 L 130 20" stroke="#FFD700" stroke-width="2" />
    <path d="M 90 30 L 130 30" stroke="#FFD700" stroke-width="3" />
    <path d="M 90 30 L 130 40" stroke="#FFD700" stroke-width="2" />
    
    <text x="50" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#005A9E" font-weight="bold">
      透视眼
    </text>
  </g>
  
  <!-- 时间标识 -->
  <g transform="translate(1600, 400)">
    <circle cx="40" cy="40" r="35" stroke="#F5A623" stroke-width="4" fill="#FFFFFF" />
    <text x="40" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#F5A623" font-weight="bold">
      09:00
    </text>
    <text x="40" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#F5A623">
      开始
    </text>
  </g>
</svg>
