<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="70px" font-weight="bold" fill="#005A9E">
    课间休息
  </text>
  
  <!-- 大脑运转图 -->
  <g transform="translate(600, 300)">
    <!-- 大脑轮廓 -->
    <path d="M 200 100 Q 150 50 100 80 Q 50 60 30 100 Q 20 150 50 180 Q 80 200 120 190 Q 160 210 200 190 Q 240 200 280 180 Q 320 150 310 100 Q 290 60 250 80 Q 220 50 200 100" 
          fill="#E6E6FA" stroke="#9C27B0" stroke-width="3" />
    
    <!-- 神经元连接 -->
    <g transform="translate(50, 80)">
      <!-- 神经元节点 -->
      <circle cx="50" cy="30" r="8" fill="#4CAF50" />
      <circle cx="120" cy="50" r="8" fill="#2196F3" />
      <circle cx="180" cy="40" r="8" fill="#FF9800" />
      <circle cx="90" cy="80" r="8" fill="#9C27B0" />
      <circle cx="150" cy="90" r="8" fill="#F5A623" />
      <circle cx="200" cy="80" r="8" fill="#FF6B6B" />
      
      <!-- 连接线 -->
      <path d="M 50 30 Q 85 40 120 50" stroke="#4CAF50" stroke-width="2" fill="none" />
      <path d="M 120 50 Q 150 45 180 40" stroke="#2196F3" stroke-width="2" fill="none" />
      <path d="M 90 80 Q 120 85 150 90" stroke="#9C27B0" stroke-width="2" fill="none" />
      <path d="M 150 90 Q 175 85 200 80" stroke="#F5A623" stroke-width="2" fill="none" />
      <path d="M 50 30 Q 70 55 90 80" stroke="#4CAF50" stroke-width="2" fill="none" />
      <path d="M 180 40 Q 190 60 200 80" stroke="#FF9800" stroke-width="2" fill="none" />
      
      <!-- 电流效果 -->
      <circle cx="85" cy="40" r="3" fill="#FFD700" opacity="0.8">
        <animate attributeName="opacity" values="0.8;0.2;0.8" dur="1s" repeatCount="indefinite" />
      </circle>
      <circle cx="135" cy="70" r="3" fill="#FFD700" opacity="0.6">
        <animate attributeName="opacity" values="0.6;0.1;0.6" dur="1.2s" repeatCount="indefinite" />
      </circle>
      <circle cx="175" cy="85" r="3" fill="#FFD700" opacity="0.7">
        <animate attributeName="opacity" values="0.7;0.2;0.7" dur="0.8s" repeatCount="indefinite" />
      </circle>
    </g>
    
    <text x="160" y="280" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333" font-weight="bold">
      大脑正在高速运转、连接神经元
    </text>
  </g>
  
  <!-- 思考题 -->
  <g transform="translate(200, 600)">
    <rect x="0" y="0" width="1520" height="150" fill="#F5A623" rx="15" opacity="0.1" />
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#F5A623">
      思考题：
    </text>
    
    <text x="760" y="90" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" fill="#333333">
      你过去哪个客户，最适合用"痛苦量化"的方法，重新沟通一次？
    </text>
    
    <!-- 问号装饰 -->
    <g transform="translate(100, 80)">
      <circle cx="30" cy="30" r="25" fill="#F5A623" opacity="0.2" />
      <text x="30" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#F5A623">
        ?
      </text>
    </g>
    
    <g transform="translate(1350, 80)">
      <circle cx="30" cy="30" r="25" fill="#F5A623" opacity="0.2" />
      <text x="30" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#F5A623">
        ?
      </text>
    </g>
  </g>
  
  <!-- 时间提示 -->
  <g transform="translate(200, 800)">
    <rect x="0" y="0" width="1520" height="100" fill="#005A9E" rx="15" />
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FFFFFF">
      15分钟后，进入高压实战对练
    </text>
    <text x="760" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFD700">
      准备好接受挑战！
    </text>
  </g>
  
  <!-- 时钟图标 -->
  <g transform="translate(1600, 300)">
    <circle cx="50" cy="50" r="40" stroke="#F5A623" stroke-width="4" fill="#FFFFFF" />
    <circle cx="50" cy="50" r="3" fill="#F5A623" />
    
    <!-- 时针 -->
    <line x1="50" y1="50" x2="50" y2="30" stroke="#F5A623" stroke-width="3" />
    <!-- 分针 -->
    <line x1="50" y1="50" x2="70" y2="35" stroke="#005A9E" stroke-width="2" />
    
    <!-- 刻度 -->
    <line x1="50" y1="15" x2="50" y2="20" stroke="#333333" stroke-width="2" />
    <line x1="85" y1="50" x2="80" y2="50" stroke="#333333" stroke-width="2" />
    <line x1="50" y1="85" x2="50" y2="80" stroke="#333333" stroke-width="2" />
    <line x1="15" y1="50" x2="20" y2="50" stroke="#333333" stroke-width="2" />
    
    <text x="50" y="120" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#F5A623">
      15分钟
    </text>
  </g>
  
  <!-- 咖啡图标 -->
  <g transform="translate(100, 800)">
    <!-- 咖啡杯 -->
    <ellipse cx="30" cy="45" rx="20" ry="15" fill="#8B4513" />
    <ellipse cx="30" cy="40" rx="18" ry="12" fill="#D2691E" />
    <ellipse cx="30" cy="35" rx="15" ry="8" fill="#F4A460" />
    
    <!-- 杯柄 -->
    <path d="M 50 40 Q 60 35 60 45 Q 60 55 50 50" stroke="#8B4513" stroke-width="3" fill="none" />
    
    <!-- 热气 -->
    <path d="M 20 25 Q 22 15 20 10" stroke="#CCCCCC" stroke-width="2" fill="none" opacity="0.6" />
    <path d="M 30 25 Q 32 15 30 10" stroke="#CCCCCC" stroke-width="2" fill="none" opacity="0.6" />
    <path d="M 40 25 Q 42 15 40 10" stroke="#CCCCCC" stroke-width="2" fill="none" opacity="0.6" />
  </g>
  
  <!-- 放松元素 -->
  <g transform="translate(1700, 800)">
    <!-- 伸展的人 -->
    <circle cx="25" cy="20" r="12" fill="#4CAF50" />
    <rect x="20" y="32" width="10" height="20" fill="#005A9E" rx="2" />
    
    <!-- 伸展的手臂 -->
    <line x1="15" y1="38" x2="5" y2="28" stroke="#4CAF50" stroke-width="3" />
    <line x1="35" y1="38" x2="45" y2="28" stroke="#4CAF50" stroke-width="3" />
    
    <text x="25" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      放松一下
    </text>
  </g>
</svg>
