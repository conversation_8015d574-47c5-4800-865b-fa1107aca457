<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="70px" font-weight="bold" fill="#005A9E">
    课间休息
  </text>
  
  <!-- 建筑师绘图场景 -->
  <g transform="translate(600, 300)">
    <!-- 绘图桌 -->
    <rect x="0" y="200" width="720" height="300" fill="#D2B48C" rx="15" />
    <rect x="20" y="180" width="680" height="40" fill="#8B4513" rx="8" />
    
    <!-- 建筑师人物 -->
    <circle cx="360" cy="150" r="40" fill="#2196F3" />
    <rect x="335" y="190" width="50" height="80" fill="#005A9E" rx="10" />
    
    <!-- 面部特征 -->
    <circle cx="350" cy="145" r="5" fill="#FFFFFF" />
    <circle cx="370" cy="145" r="5" fill="#FFFFFF" />
    <path d="M 350 160 Q 360 165 370 160" stroke="#FFFFFF" stroke-width="3" fill="none" />
    
    <!-- 安全帽 -->
    <ellipse cx="360" cy="125" rx="35" ry="18" fill="#FFD700" />
    <rect x="340" y="115" width="40" height="12" fill="#FFD700" />
    
    <!-- 绘图动作 -->
    <line x1="320" y1="210" x2="280" y2="240" stroke="#2196F3" stroke-width="4" />
    <circle cx="275" cy="245" r="5" fill="#F5A623" />
    
    <!-- 蓝图纸张 -->
    <rect x="200" y="240" width="400" height="280" fill="#E6F3FF" stroke="#2196F3" stroke-width="3" rx="8" />
    
    <!-- 蓝图内容 -->
    <text x="400" y="270" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#2196F3" font-weight="bold">
      客户业务蓝图
    </text>
    
    <!-- 建筑结构图 -->
    <g transform="translate(250, 290)">
      <!-- 基础结构 -->
      <rect x="0" y="150" width="300" height="20" fill="#2196F3" opacity="0.3" />
      
      <!-- 支柱 -->
      <rect x="50" y="50" width="20" height="120" fill="#2196F3" opacity="0.5" />
      <rect x="130" y="70" width="20" height="100" fill="#2196F3" opacity="0.5" />
      <rect x="210" y="60" width="20" height="110" fill="#2196F3" opacity="0.5" />
      
      <!-- 连接线 -->
      <line x1="60" y1="50" x2="140" y2="70" stroke="#2196F3" stroke-width="2" />
      <line x1="140" y1="70" x2="220" y2="60" stroke="#2196F3" stroke-width="2" />
      
      <!-- 标注 -->
      <text x="60" y="45" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#2196F3">
        现状
      </text>
      <text x="140" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#2196F3">
        方案
      </text>
      <text x="220" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#2196F3">
        愿景
      </text>
    </g>
    
    <!-- 绘图工具 -->
    <g transform="translate(150, 300)">
      <!-- 直尺 -->
      <rect x="0" y="0" width="80" height="8" fill="#FFD700" rx="2" />
      <line x1="10" y1="0" x2="10" y2="8" stroke="#333333" stroke-width="1" />
      <line x1="20" y1="0" x2="20" y2="8" stroke="#333333" stroke-width="1" />
      <line x1="30" y1="0" x2="30" y2="8" stroke="#333333" stroke-width="1" />
      
      <!-- 圆规 -->
      <g transform="translate(0, 20)">
        <line x1="15" y1="0" x2="15" y2="25" stroke="#333333" stroke-width="2" />
        <line x1="25" y1="0" x2="25" y2="25" stroke="#333333" stroke-width="2" />
        <circle cx="15" cy="0" r="3" fill="#333333" />
        <circle cx="25" cy="25" r="2" fill="#F5A623" />
      </g>
      
      <!-- 铅笔 -->
      <g transform="translate(0, 60)">
        <rect x="0" y="0" width="60" height="6" fill="#FFD700" rx="3" />
        <polygon points="60,0 70,3 60,6" fill="#F5A623" />
        <rect x="5" y="1" width="50" height="4" fill="#333333" rx="1" />
      </g>
    </g>
  </g>
  
  <!-- 休息提示 -->
  <g transform="translate(200, 650)">
    <rect x="0" y="0" width="1520" height="120" fill="#4CAF50" rx="15" />
    <text x="760" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#FFFFFF">
      15分钟后，开始我们自己的"蓝图设计"
    </text>
    <text x="760" y="90" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF">
      请稍作休息，准备迎接下午的实战工作坊
    </text>
  </g>
  
  <!-- 时钟图标 -->
  <g transform="translate(200, 300)">
    <circle cx="60" cy="60" r="50" stroke="#F5A623" stroke-width="6" fill="#FFFFFF" />
    <circle cx="60" cy="60" r="5" fill="#F5A623" />
    
    <!-- 时针指向3点 -->
    <line x1="60" y1="60" x2="60" y2="30" stroke="#F5A623" stroke-width="4" />
    <line x1="60" y1="60" x2="90" y2="60" stroke="#005A9E" stroke-width="3" />
    
    <!-- 数字 -->
    <text x="60" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">12</text>
    <text x="95" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">3</text>
    <text x="60" y="105" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">6</text>
    <text x="25" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">9</text>
    
    <text x="60" y="140" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333" font-weight="bold">
      15分钟
    </text>
  </g>
  
  <!-- 咖啡图标 -->
  <g transform="translate(1600, 300)">
    <!-- 咖啡杯 -->
    <ellipse cx="60" cy="100" rx="40" ry="15" fill="#8B4513" />
    <rect x="20" y="60" width="80" height="40" fill="#D2691E" rx="8" />
    <ellipse cx="60" cy="60" rx="40" ry="15" fill="#F4A460" />
    
    <!-- 咖啡液面 -->
    <ellipse cx="60" cy="65" rx="35" ry="12" fill="#8B4513" />
    
    <!-- 杯柄 -->
    <path d="M 100 75 Q 120 75 120 85 Q 120 95 100 95" stroke="#D2691E" stroke-width="6" fill="none" />
    
    <!-- 热气 -->
    <path d="M 45 45 Q 50 35 45 25" stroke="#CCCCCC" stroke-width="2" fill="none" />
    <path d="M 60 45 Q 65 35 60 25" stroke="#CCCCCC" stroke-width="2" fill="none" />
    <path d="M 75 45 Q 80 35 75 25" stroke="#CCCCCC" stroke-width="2" fill="none" />
    
    <text x="60" y="140" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      休息时间
    </text>
  </g>
  
  <!-- 底部装饰 -->
  <rect x="200" y="800" width="1520" height="80" fill="#F5A623" rx="15" />
  <text x="960" y="830" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#FFFFFF">
    理论学习告一段落，实战演练即将开始
  </text>
  <text x="960" y="860" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF">
    从"诊断"到"开方"的完整闭环等待着我们
  </text>
</svg>
