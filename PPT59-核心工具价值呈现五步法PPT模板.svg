<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="140" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="54px" font-weight="bold" fill="#005A9E">
    核心工具：《价值呈现五步法PPT模板》
  </text>
  
  <!-- 五步法流程 -->
  <g transform="translate(200, 200)">
    <!-- 步骤1：英雄的困境 -->
    <g transform="translate(0, 0)">
      <rect x="0" y="0" width="280" height="150" fill="#FF6B6B" rx="15" opacity="0.1" />
      <rect x="20" y="20" width="240" height="40" fill="#FF6B6B" rx="8" />
      <text x="140" y="45" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#FFFFFF">
        1. 英雄的困境
      </text>
      <text x="140" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FF6B6B" font-weight="bold">
        The Crossroads
      </text>
      
      <text x="140" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        建立共鸣
      </text>
      <text x="140" y="125" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#666666">
        只讲痛点，让客户觉得"你懂我"
      </text>
    </g>
    
    <!-- 箭头1 -->
    <g transform="translate(300, 75)">
      <path d="M 0 0 L 20 -10 L 20 -5 L 40 -5 L 40 5 L 20 5 L 20 10 Z" fill="#F5A623" />
    </g>
    
    <!-- 步骤2：胜利的曙光 -->
    <g transform="translate(360, 0)">
      <rect x="0" y="0" width="280" height="150" fill="#FFD700" rx="15" opacity="0.1" />
      <rect x="20" y="20" width="240" height="40" fill="#FFD700" rx="8" />
      <text x="140" y="45" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#333333">
        2. 胜利的曙光
      </text>
      <text x="140" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FF9800" font-weight="bold">
        A Glimpse of the Future
      </text>
      
      <text x="140" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        创造向往
      </text>
      <text x="140" y="125" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#666666">
        描绘愿景，让客户觉得"我想要"
      </text>
    </g>
    
    <!-- 箭头2 -->
    <g transform="translate(660, 75)">
      <path d="M 0 0 L 20 -10 L 20 -5 L 40 -5 L 40 5 L 20 5 L 20 10 Z" fill="#F5A623" />
    </g>
    
    <!-- 步骤3：神器的力量 -->
    <g transform="translate(720, 0)">
      <rect x="0" y="0" width="280" height="150" fill="#4CAF50" rx="15" opacity="0.1" />
      <rect x="20" y="20" width="240" height="40" fill="#4CAF50" rx="8" />
      <text x="140" y="45" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#FFFFFF">
        3. 神器的力量
      </text>
      <text x="140" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#4CAF50" font-weight="bold">
        The Blueprint
      </text>
      
      <text x="140" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        给出路径
      </text>
      <text x="140" y="125" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#666666">
        展示蓝图，让客户觉得"原来可以这样"
      </text>
    </g>
    
    <!-- 箭头3 -->
    <g transform="translate(1020, 75)">
      <path d="M 0 0 L 20 -10 L 20 -5 L 40 -5 L 40 5 L 20 5 L 20 10 Z" fill="#F5A623" />
    </g>
    
    <!-- 步骤4：导师的召唤 -->
    <g transform="translate(1080, 0)">
      <rect x="0" y="0" width="280" height="150" fill="#2196F3" rx="15" opacity="0.1" />
      <rect x="20" y="20" width="240" height="40" fill="#2196F3" rx="8" />
      <text x="140" y="45" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#FFFFFF">
        4. 导师的召唤
      </text>
      <text x="140" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#2196F3" font-weight="bold">
        The Proof
      </text>
      
      <text x="140" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        建立信任
      </text>
      <text x="140" y="125" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#666666">
        拿出证明，让客户觉得"我相信你"
      </text>
    </g>
    
    <!-- 向下箭头 -->
    <g transform="translate(1220, 170)">
      <path d="M 0 0 L -10 20 L -5 20 L -5 40 L 5 40 L 5 20 L 10 20 Z" fill="#F5A623" />
    </g>
    
    <!-- 步骤5：英雄的抉择 -->
    <g transform="translate(1080, 230)">
      <rect x="0" y="0" width="280" height="150" fill="#9C27B0" rx="15" opacity="0.1" />
      <rect x="20" y="20" width="240" height="40" fill="#9C27B0" rx="8" />
      <text x="140" y="45" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#FFFFFF">
        5. 英雄的抉择
      </text>
      <text x="140" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#9C27B0" font-weight="bold">
        The First Step
      </text>
      
      <text x="140" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        推动行动
      </text>
      <text x="140" y="125" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#666666">
        给出下一步，让客户觉得"没压力，可以试试"
      </text>
    </g>
  </g>
  
  <!-- 详细说明 -->
  <g transform="translate(200, 450)">
    <rect x="0" y="0" width="1520" height="300" fill="#F8F9FA" rx="15" />
    <text x="760" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#005A9E" font-weight="bold">
      每一页的核心目标和设计要点
    </text>
    
    <g transform="translate(50, 60)">
      <!-- 第一页说明 -->
      <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#FF6B6B" font-weight="bold">
        第一页：
      </text>
      <text x="80" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
        只讲痛点，让客户觉得"你懂我"
      </text>
      
      <!-- 第二页说明 -->
      <text x="0" y="60" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#FFD700" font-weight="bold">
        第二页：
      </text>
      <text x="80" y="60" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
        描绘愿景，让客户觉得"我想要"
      </text>
      
      <!-- 第三页说明 -->
      <text x="0" y="95" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#4CAF50" font-weight="bold">
        第三页：
      </text>
      <text x="80" y="95" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
        展示蓝图，让客户觉得"原来可以这样"
      </text>
      
      <!-- 第四页说明 -->
      <text x="0" y="130" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#2196F3" font-weight="bold">
        第四页：
      </text>
      <text x="80" y="130" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
        拿出证明，让客户觉得"我相信你"
      </text>
      
      <!-- 第五页说明 -->
      <text x="0" y="165" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#9C27B0" font-weight="bold">
        第五页：
      </text>
      <text x="80" y="165" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
        给出下一步，让客户觉得"没压力，可以试试"
      </text>
      
      <!-- 核心理念 -->
      <rect x="600" y="20" width="820" height="180" fill="#005A9E" rx="10" opacity="0.1" />
      <text x="1010" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#005A9E" font-weight="bold">
        核心理念：客户英雄之旅
      </text>
      
      <text x="1010" y="85" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        • 客户永远是英雄路克
      </text>
      <text x="1010" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        • 你是给他光剑的欧比旺
      </text>
      <text x="1010" y="135" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        • 你的方案就是那把光剑
      </text>
      <text x="1010" y="170" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        • 启动人类心智的"故事模式"
      </text>
    </g>
  </g>
  
  <!-- 底部总结 -->
  <rect x="200" y="780" width="1520" height="120" fill="#F5A623" rx="15" />
  <text x="960" y="820" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    顶尖咨询顾问和好莱坞导演都在使用的叙事框架
  </text>
  <text x="960" y="860" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF">
    让你的方案从"说明文"变成"英雄史诗"
  </text>
  
  <!-- PPT图标 -->
  <g transform="translate(100, 200)">
    <rect x="20" y="20" width="60" height="45" fill="#FF6B6B" rx="5" />
    <rect x="25" y="25" width="50" height="35" fill="#FFFFFF" rx="3" />
    <text x="50" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FF6B6B" font-weight="bold">
      PPT
    </text>
    <text x="50" y="52" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8px" fill="#333333">
      模板
    </text>
    
    <text x="50" y="85" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      五步法模板
    </text>
  </g>
  
  <!-- 故事书图标 -->
  <g transform="translate(1700, 200)">
    <rect x="20" y="20" width="60" height="45" fill="#4CAF50" rx="5" />
    <rect x="25" y="25" width="50" height="35" fill="#FFFFFF" rx="3" />
    
    <!-- 故事元素 -->
    <circle cx="40" cy="35" r="3" fill="#FFD700" />
    <rect x="37" y="40" width="6" height="8" fill="#2196F3" rx="1" />
    <circle cx="60" cy="40" r="3" fill="#FF6B6B" />
    <rect x="57" y="45" width="6" height="8" fill="#9C27B0" rx="1" />
    
    <text x="50" y="85" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      故事框架
    </text>
  </g>
</svg>
