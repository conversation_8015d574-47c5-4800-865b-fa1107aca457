<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="58px" font-weight="bold" fill="#005A9E">
    顾问级SPIN：与业务价值链的深度融合
  </text>
  
  <!-- 业务价值链 -->
  <g transform="translate(200, 220)">
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#F5A623">
      业务价值链
    </text>
    
    <!-- 价值链流程 -->
    <g transform="translate(0, 80)">
      <!-- 研发 -->
      <rect x="0" y="0" width="300" height="100" fill="#4CAF50" rx="15" opacity="0.1" />
      <text x="150" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#4CAF50">
        研发
      </text>
      <text x="150" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
        产品创新与设计
      </text>
      
      <!-- 箭头 -->
      <path d="M 310 50 L 350 50" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
      
      <!-- 生产 -->
      <rect x="360" y="0" width="300" height="100" fill="#2196F3" rx="15" opacity="0.1" />
      <text x="510" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#2196F3">
        生产
      </text>
      <text x="510" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
        制造与质量控制
      </text>
      
      <!-- 箭头 -->
      <path d="M 670 50 L 710 50" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
      
      <!-- 营销 -->
      <rect x="720" y="0" width="300" height="100" fill="#FF9800" rx="15" opacity="0.1" />
      <text x="870" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FF9800">
        营销
      </text>
      <text x="870" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
        市场推广与销售
      </text>
      
      <!-- 箭头 -->
      <path d="M 1030 50 L 1070 50" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
      
      <!-- 服务 -->
      <rect x="1080" y="0" width="300" height="100" fill="#9C27B0" rx="15" opacity="0.1" />
      <text x="1230" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#9C27B0">
        服务
      </text>
      <text x="1230" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
        客户支持与维护
      </text>
    </g>
  </g>
  
  <!-- 手术刀比喻 -->
  <g transform="translate(200, 400)">
    <text x="0" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#F5A623">
      SPIN问题，要像手术刀一样，精准切入每个环节
    </text>
    
    <!-- 手术刀图标 -->
    <g transform="translate(1200, 0)">
      <line x1="0" y1="20" x2="80" y2="20" stroke="#C0C0C0" stroke-width="4" />
      <path d="M 80 20 L 100 15 L 100 25 Z" fill="#C0C0C0" />
      <rect x="10" y="15" width="30" height="10" fill="#333333" rx="2" />
      
      <!-- 切割线 -->
      <path d="M 110 15 L 130 5" stroke="#FF6B6B" stroke-width="2" stroke-dasharray="3,3" />
      <path d="M 110 25 L 130 35" stroke="#FF6B6B" stroke-width="2" stroke-dasharray="3,3" />
      
      <text x="50" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        精准切入
      </text>
    </g>
  </g>
  
  <!-- 生产环节示例 -->
  <g transform="translate(200, 500)">
    <rect x="0" y="0" width="1520" height="200" fill="#2196F3" rx="15" opacity="0.1" />
    <text x="760" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#2196F3">
      生产环节示例：
    </text>
    
    <!-- SPIN四个问题 -->
    <g transform="translate(50, 60)">
      <!-- S - 背景 -->
      <g transform="translate(0, 0)">
        <circle cx="20" cy="20" r="15" fill="#4CAF50" />
        <text x="20" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">S</text>
        <text x="50" y="15" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#4CAF50">
          背景 (Situation):
        </text>
        <text x="50" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
          产线自动化水平？
        </text>
      </g>
      
      <!-- P - 难点 -->
      <g transform="translate(350, 0)">
        <circle cx="20" cy="20" r="15" fill="#FF9800" />
        <text x="20" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">P</text>
        <text x="50" y="15" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#FF9800">
          难点 (Problem):
        </text>
        <text x="50" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
          设备意外停机频率？
        </text>
      </g>
      
      <!-- I - 暗示 -->
      <g transform="translate(700, 0)">
        <circle cx="20" cy="20" r="15" fill="#FF6B6B" />
        <text x="20" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">I</text>
        <text x="50" y="15" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#FF6B6B">
          暗示 (Implication):
        </text>
        <text x="50" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
          停机对订单交付和客户信任的连锁影响？
        </text>
      </g>
      
      <!-- N - 效益 -->
      <g transform="translate(1150, 0)">
        <circle cx="20" cy="20" r="15" fill="#9C27B0" />
        <text x="20" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">N</text>
        <text x="50" y="15" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#9C27B0">
          效益 (Need-payoff):
        </text>
        <text x="50" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
          如果能提前预警，对生产稳定性有多大提升？
        </text>
      </g>
    </g>
    
    <!-- 连接线 -->
    <g transform="translate(50, 120)">
      <path d="M 35 0 Q 200 -20 365 0" stroke="#F5A623" stroke-width="3" fill="none" />
      <path d="M 365 0 Q 530 -20 715 0" stroke="#F5A623" stroke-width="3" fill="none" />
      <path d="M 715 0 Q 930 -20 1165 0" stroke="#F5A623" stroke-width="3" fill="none" />
    </g>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#F5A623" />
    </marker>
  </defs>
  
  <!-- 底部强调 -->
  <rect x="200" y="750" width="1520" height="120" fill="#005A9E" rx="15" />
  <text x="960" y="790" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    顾问级的SPIN，不是干巴巴地问S、P、I、N
  </text>
  <text x="960" y="830" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    而是把这些问题，像手术刀一样，精准地嵌入到客户的核心业务流程中
  </text>
  
  <!-- 价值链地图提示 -->
  <g transform="translate(1400, 750)">
    <rect x="0" y="0" width="120" height="80" fill="#FFFFFF" stroke="#F5A623" stroke-width="2" rx="5" />
    <text x="60" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#F5A623" font-weight="bold">
      详细地图
    </text>
    <text x="60" y="45" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      见手册
    </text>
    <text x="60" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      第X页
    </text>
  </g>
</svg>
