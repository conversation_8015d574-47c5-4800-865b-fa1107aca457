<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="66px" font-weight="bold" fill="#005A9E">
    新大陆的"身份证"
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="250" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="44px" font-weight="bold" fill="#F5A623">
    首席增长顾问 V.A.L.U.E. 模型
  </text>
  
  <!-- V.A.L.U.E. 盾牌徽章 -->
  <g transform="translate(760, 320)">
    <!-- 盾牌外框 -->
    <path d="M 200 50 Q 250 30 300 50 L 320 150 Q 320 200 300 240 L 250 300 L 200 240 Q 180 200 180 150 Z" 
          fill="#005A9E" stroke="#F5A623" stroke-width="4" />
    
    <!-- 盾牌内框 -->
    <path d="M 200 70 Q 240 55 280 70 L 295 150 Q 295 190 280 220 L 240 270 L 200 220 Q 185 190 185 150 Z" 
          fill="#FFFFFF" />
    
    <!-- V.A.L.U.E. 文字 -->
    <text x="240" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#005A9E">
      V.A.L.U.E.
    </text>
    
    <!-- 星星装饰 -->
    <polygon points="240,160 245,170 255,170 247,178 250,188 240,182 230,188 233,178 225,170 235,170" fill="#F5A623" />
  </g>
  
  <!-- 五个维度详细说明 -->
  <g transform="translate(200, 450)">
    <!-- V - Value Partner -->
    <rect x="0" y="0" width="280" height="100" fill="#005A9E" rx="10" />
    <text x="20" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#F5A623">V</text>
    <text x="50" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#FFFFFF">Value Partner</text>
    <text x="20" y="55" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FFFFFF">价值伙伴</text>
    <text x="20" y="80" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#FFFFFF">共创商业价值</text>
  </g>
  
  <g transform="translate(520, 450)">
    <!-- A - Agile Responder -->
    <rect x="0" y="0" width="280" height="100" fill="#F5A623" rx="10" />
    <text x="20" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#FFFFFF">A</text>
    <text x="50" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#FFFFFF">Agile Responder</text>
    <text x="20" y="55" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FFFFFF">敏捷响应者</text>
    <text x="20" y="80" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#FFFFFF">快速反应部队</text>
  </g>
  
  <g transform="translate(840, 450)">
    <!-- L - Learning Expert -->
    <rect x="0" y="0" width="280" height="100" fill="#005A9E" rx="10" />
    <text x="20" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#F5A623">L</text>
    <text x="50" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#FFFFFF">Learning Expert</text>
    <text x="20" y="55" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FFFFFF">学习型专家</text>
    <text x="20" y="80" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#FFFFFF">持续精进能力</text>
  </g>
  
  <g transform="translate(1160, 450)">
    <!-- U - Unique Experience Provider -->
    <rect x="0" y="0" width="280" height="100" fill="#F5A623" rx="10" />
    <text x="20" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#FFFFFF">U</text>
    <text x="50" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#FFFFFF">Unique Experience</text>
    <text x="50" y="50" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#FFFFFF">Provider</text>
    <text x="20" y="75" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FFFFFF">独特体验提供者</text>
  </g>
  
  <g transform="translate(1480, 450)">
    <!-- E - Efficient Executor -->
    <rect x="0" y="0" width="280" height="100" fill="#005A9E" rx="10" />
    <text x="20" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#F5A623">E</text>
    <text x="50" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#FFFFFF">Efficient Executor</text>
    <text x="20" y="55" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FFFFFF">高效执行者</text>
    <text x="20" y="80" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#FFFFFF">言必信行必果</text>
  </g>
  
  <!-- 连接线 -->
  <line x1="340" y1="500" x2="520" y2="500" stroke="#CCCCCC" stroke-width="2" stroke-dasharray="5,5" />
  <line x1="800" y1="500" x2="840" y2="500" stroke="#CCCCCC" stroke-width="2" stroke-dasharray="5,5" />
  <line x1="1120" y1="500" x2="1160" y2="500" stroke="#CCCCCC" stroke-width="2" stroke-dasharray="5,5" />
  <line x1="1440" y1="500" x2="1480" y2="500" stroke="#CCCCCC" stroke-width="2" stroke-dasharray="5,5" />
  
  <!-- 底部说明 -->
  <text x="960" y="700" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" fill="#333333">
    五维能力模型 · 指引持续精进的能力地图
  </text>
  
  <!-- 价值强调 -->
  <rect x="300" y="750" width="1320" height="80" fill="#F5A623" rx="10" opacity="0.1" />
  <text x="960" y="800" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#F5A623">
    V.A.L.U.E = 价值 · 你的专业身份认同与能力框架
  </text>
</svg>
