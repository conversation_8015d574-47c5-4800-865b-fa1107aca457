<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="66px" font-weight="bold" fill="#005A9E">
    E - Efficient Executor (高效执行者)
  </text>
  
  <!-- 核心理念 -->
  <text x="960" y="230" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="44px" font-weight="bold" fill="#F5A623">
    从"说到"到"做到"的惊险一跃
  </text>
  
  <!-- 顾问的两个角色 -->
  <text x="300" y="320" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40px" font-weight="bold" fill="#F5A623">
    顾问的两个角色：
  </text>
  
  <!-- 对外角色 -->
  <g transform="translate(300, 380)">
    <rect x="0" y="0" width="500" height="100" fill="#005A9E" rx="15" opacity="0.1" />
    <text x="50" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#005A9E">
      对外：
    </text>
    <text x="150" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#333333">
      客户的战略伙伴
    </text>
    
    <!-- 战略伙伴图标 -->
    <g transform="translate(350, 50)">
      <circle cx="30" cy="25" r="20" fill="#005A9E" />
      <circle cx="70" cy="25" r="20" fill="#F5A623" />
      <path d="M 30 45 Q 50 65 70 45" stroke="#4CAF50" stroke-width="4" fill="none" />
    </g>
  </g>
  
  <!-- 对内角色 -->
  <g transform="translate(900, 380)">
    <rect x="0" y="0" width="600" height="100" fill="#F5A623" rx="15" opacity="0.1" />
    <text x="50" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#F5A623">
      对内：
    </text>
    <text x="150" y="35" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#333333">
      资源的协调大师 &amp; 项目经理
    </text>
    
    <!-- 协调图标 -->
    <g transform="translate(450, 50)">
      <circle cx="50" cy="25" r="15" fill="#F5A623" />
      <circle cx="20" cy="10" r="10" fill="#005A9E" />
      <circle cx="80" cy="10" r="10" fill="#005A9E" />
      <circle cx="20" cy="40" r="10" fill="#005A9E" />
      <circle cx="80" cy="40" r="10" fill="#005A9E" />
      
      <line x1="35" y1="15" x2="35" y2="15" stroke="#333333" stroke-width="2" />
      <line x1="65" y1="15" x2="65" y2="15" stroke="#333333" stroke-width="2" />
      <line x1="35" y1="35" x2="35" y2="35" stroke="#333333" stroke-width="2" />
      <line x1="65" y1="35" x2="65" y2="35" stroke="#333333" stroke-width="2" />
    </g>
  </g>
  
  <!-- 齿轮运转图标 -->
  <g transform="translate(1400, 250)">
    <!-- 大齿轮 -->
    <circle cx="80" cy="80" r="60" fill="#005A9E" opacity="0.3" />
    <circle cx="80" cy="80" r="45" fill="#FFFFFF" />
    <circle cx="80" cy="80" r="30" fill="#005A9E" />
    
    <!-- 齿轮齿 -->
    <rect x="75" y="15" width="10" height="15" fill="#005A9E" />
    <rect x="75" y="130" width="10" height="15" fill="#005A9E" />
    <rect x="15" y="75" width="15" height="10" fill="#005A9E" />
    <rect x="130" y="75" width="15" height="10" fill="#005A9E" />
    
    <!-- 小齿轮 -->
    <circle cx="150" cy="50" r="35" fill="#F5A623" opacity="0.3" />
    <circle cx="150" cy="50" r="25" fill="#FFFFFF" />
    <circle cx="150" cy="50" r="15" fill="#F5A623" />
    
    <!-- 小齿轮齿 -->
    <rect x="147" y="10" width="6" height="10" fill="#F5A623" />
    <rect x="147" y="80" width="6" height="10" fill="#F5A623" />
    <rect x="110" y="47" width="10" height="6" fill="#F5A623" />
    <rect x="180" y="47" width="10" height="6" fill="#F5A623" />
    
    <text x="115" y="180" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      高效运转
    </text>
  </g>
  
  <!-- 内部协调流程 -->
  <g transform="translate(200, 520)">
    <rect x="0" y="0" width="1200" height="150" fill="#F5F5F5" stroke="#005A9E" stroke-width="3" rx="15" />
    <text x="600" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#005A9E">
      内部资源协调流程
    </text>
    
    <!-- 流程步骤 -->
    <g transform="translate(50, 50)">
      <!-- 技术部门 -->
      <rect x="0" y="0" width="120" height="60" fill="#4CAF50" rx="10" />
      <text x="60" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" font-weight="bold" fill="#FFFFFF">技术</text>
      <text x="60" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#FFFFFF">方案设计</text>
      
      <!-- 箭头 -->
      <path d="M 130 30 L 180 30" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
      
      <!-- 交付部门 -->
      <rect x="190" y="0" width="120" height="60" fill="#2196F3" rx="10" />
      <text x="250" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" font-weight="bold" fill="#FFFFFF">交付</text>
      <text x="250" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#FFFFFF">实施部署</text>
      
      <!-- 箭头 -->
      <path d="M 320 30 L 370 30" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
      
      <!-- 法务部门 -->
      <rect x="380" y="0" width="120" height="60" fill="#9C27B0" rx="10" />
      <text x="440" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" font-weight="bold" fill="#FFFFFF">法务</text>
      <text x="440" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#FFFFFF">合同审核</text>
      
      <!-- 箭头 -->
      <path d="M 510 30 L 560 30" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
      
      <!-- 客户交付 -->
      <rect x="570" y="0" width="120" height="60" fill="#FF6B6B" rx="10" />
      <text x="630" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" font-weight="bold" fill="#FFFFFF">客户</text>
      <text x="630" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#FFFFFF">成功交付</text>
      
      <!-- 协调者 -->
      <circle cx="345" cy="-30" r="25" fill="#F5A623" />
      <text x="345" y="-25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" font-weight="bold" fill="#FFFFFF">顾问</text>
      
      <!-- 协调线 -->
      <line x1="345" y1="-5" x2="60" y2="0" stroke="#F5A623" stroke-width="2" stroke-dasharray="3,3" />
      <line x1="345" y1="-5" x2="250" y2="0" stroke="#F5A623" stroke-width="2" stroke-dasharray="3,3" />
      <line x1="345" y1="-5" x2="440" y2="0" stroke="#F5A623" stroke-width="2" stroke-dasharray="3,3" />
      <line x1="345" y1="-5" x2="630" y2="0" stroke="#F5A623" stroke-width="2" stroke-dasharray="3,3" />
    </g>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#F5A623" />
    </marker>
  </defs>
  
  <!-- 信任落脚点 -->
  <text x="300" y="750" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40px" font-weight="bold" fill="#F5A623">
    信任的落脚点：
  </text>
  <text x="550" y="750" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" fill="#333333">
    言必信，行必果
  </text>
  
  <!-- 底部强调 -->
  <rect x="200" y="800" width="1520" height="80" fill="#F5A623" rx="10" />
  <text x="960" y="850" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FFFFFF">
    高效执行者：言必信，行必果 - 专业人士最后的尊严
  </text>
</svg>
