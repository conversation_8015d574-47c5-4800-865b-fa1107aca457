<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="66px" font-weight="bold" fill="#005A9E">
    L - Learning Expert (学习型专家)
  </text>
  
  <!-- 学习的三个层次标题 -->
  <text x="960" y="230" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="44px" font-weight="bold" fill="#F5A623">
    学习的三个层次：
  </text>
  
  <!-- 大脑图标 -->
  <g transform="translate(1400, 300)">
    <!-- 大脑轮廓 -->
    <path d="M 100 50 Q 80 30 60 40 Q 40 50 45 70 Q 30 80 35 100 Q 40 120 60 125 Q 80 130 100 125 Q 120 130 140 125 Q 160 120 165 100 Q 170 80 155 70 Q 160 50 140 40 Q 120 30 100 50 Z" 
          fill="#005A9E" opacity="0.2" />
    
    <!-- 大脑分区 -->
    <circle cx="70" cy="70" r="15" fill="#F5A623" opacity="0.6" />
    <text x="70" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FFFFFF">产品</text>
    
    <circle cx="100" cy="80" r="15" fill="#005A9E" opacity="0.6" />
    <text x="100" y="85" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FFFFFF">行业</text>
    
    <circle cx="130" cy="70" r="15" fill="#4CAF50" opacity="0.6" />
    <text x="130" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FFFFFF">商业</text>
    
    <!-- 神经连接线 -->
    <line x1="85" y1="70" x2="85" y2="80" stroke="#333333" stroke-width="1" opacity="0.5" />
    <line x1="115" y1="80" x2="115" y2="70" stroke="#333333" stroke-width="1" opacity="0.5" />
    <line x1="70" y1="85" x2="100" y2="95" stroke="#333333" stroke-width="1" opacity="0.5" />
    <line x1="130" y1="85" x2="100" y2="95" stroke="#333333" stroke-width="1" opacity="0.5" />
  </g>
  
  <!-- 第一层次：精通产品与方案 -->
  <g transform="translate(200, 320)">
    <rect x="0" y="0" width="450" height="150" fill="#F5A623" rx="15" opacity="0.1" />
    <text x="225" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#F5A623">
      精通产品与方案
    </text>
    <text x="225" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
      (know your product)
    </text>
    
    <!-- 产品图标 -->
    <g transform="translate(50, 80)">
      <rect x="0" y="0" width="60" height="40" fill="#F5A623" rx="5" />
      <rect x="10" y="10" width="40" height="20" fill="#FFFFFF" rx="3" />
      <circle cx="30" cy="20" r="5" fill="#F5A623" />
    </g>
    
    <text x="225" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      基本功：深度理解产品功能、技术架构、应用场景
    </text>
    <text x="225" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      能够清晰阐述产品价值与差异化优势
    </text>
  </g>
  
  <!-- 第二层次：洞察客户与行业 -->
  <g transform="translate(735, 320)">
    <rect x="0" y="0" width="450" height="150" fill="#005A9E" rx="15" opacity="0.1" />
    <text x="225" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#005A9E">
      洞察客户与行业
    </text>
    <text x="225" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
      (know your customer)
    </text>
    
    <!-- 行业图标 -->
    <g transform="translate(50, 80)">
      <rect x="0" y="0" width="15" height="40" fill="#005A9E" />
      <rect x="20" y="10" width="15" height="30" fill="#005A9E" />
      <rect x="40" y="20" width="15" height="20" fill="#005A9E" />
      <rect x="60" y="5" width="15" height="35" fill="#005A9E" />
    </g>
    
    <text x="225" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      平等对话前提：深入了解客户行业趋势、痛点
    </text>
    <text x="225" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      理解客户业务模式、竞争环境、发展战略
    </text>
  </g>
  
  <!-- 第三层次：跨界涉猎商业知识 -->
  <g transform="translate(200, 520)">
    <rect x="0" y="0" width="985" height="150" fill="#4CAF50" rx="15" opacity="0.1" />
    <text x="492" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#4CAF50">
      跨界涉猎商业知识
    </text>
    <text x="492" y="65" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
      (know the world)
    </text>
    
    <!-- 跨界知识图标 -->
    <g transform="translate(50, 80)">
      <circle cx="30" cy="30" r="25" fill="#4CAF50" opacity="0.3" />
      <circle cx="60" cy="30" r="25" fill="#F5A623" opacity="0.3" />
      <circle cx="90" cy="30" r="25" fill="#005A9E" opacity="0.3" />
      <circle cx="45" cy="50" r="25" fill="#FF6B6B" opacity="0.3" />
      <circle cx="75" cy="50" r="25" fill="#9C27B0" opacity="0.3" />
    </g>
    
    <text x="492" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      决定格局视野：管理学、经济学、心理学、营销学、战略学...
    </text>
    <text x="492" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
      能给客户带来跨界思维碰撞与"惊喜"洞察
    </text>
  </g>
  
  <!-- 知识保质期提醒 -->
  <g transform="translate(1300, 520)">
    <rect x="0" y="0" width="300" height="150" fill="#FF6B6B" rx="15" opacity="0.1" />
    <text x="150" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#FF6B6B">
      知识保质期警告
    </text>
    
    <!-- 牛奶盒图标 -->
    <g transform="translate(120, 50)">
      <rect x="0" y="0" width="60" height="80" fill="#FFFFFF" stroke="#FF6B6B" stroke-width="2" rx="5" />
      <rect x="10" y="10" width="40" height="15" fill="#FF6B6B" />
      <text x="30" y="22" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8px" fill="#FFFFFF">知识</text>
      <text x="30" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FF6B6B">过期</text>
    </g>
    
    <text x="150" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      知识保质期可能比牛奶还短
    </text>
    <text x="150" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      停止学习 = 专业价值归零
    </text>
  </g>
  
  <!-- 底部强调 -->
  <rect x="200" y="750" width="1520" height="80" fill="#4CAF50" rx="10" />
  <text x="960" y="800" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FFFFFF">
    学习型专家：产品精通 + 行业洞察 + 商业视野
  </text>
</svg>
