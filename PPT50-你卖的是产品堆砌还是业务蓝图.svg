<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="58px" font-weight="bold" fill="#005A9E">
    你卖的是"产品堆砌"还是"业务蓝图"？
  </text>
  
  <!-- 左侧：产品堆砌式方案 -->
  <g transform="translate(200, 220)">
    <rect x="0" y="0" width="650" height="450" fill="#FF6B6B" rx="15" opacity="0.1" />
    <text x="325" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FF6B6B">
      "产品堆砌"式方案
    </text>
    <text x="325" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#666666">
      (伪解决方案)
    </text>
    
    <!-- 混乱的PPT截图模拟 -->
    <g transform="translate(50, 100)">
      <rect x="0" y="0" width="550" height="250" fill="#FFFFFF" stroke="#FF6B6B" stroke-width="3" rx="10" />
      
      <!-- 混乱的技术术语 -->
      <rect x="20" y="20" width="100" height="30" fill="#FF6B6B" rx="5" />
      <text x="70" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">
        AI算法
      </text>
      
      <rect x="140" y="20" width="120" height="30" fill="#FF9800" rx="5" />
      <text x="200" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">
        云计算平台
      </text>
      
      <rect x="280" y="20" width="100" height="30" fill="#9C27B0" rx="5" />
      <text x="330" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">
        大数据
      </text>
      
      <rect x="400" y="20" width="120" height="30" fill="#2196F3" rx="5" />
      <text x="460" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">
        区块链技术
      </text>
      
      <!-- 更多混乱的元素 -->
      <rect x="20" y="70" width="80" height="25" fill="#4CAF50" rx="3" />
      <text x="60" y="87" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FFFFFF">
        IoT传感器
      </text>
      
      <rect x="120" y="70" width="90" height="25" fill="#FF6B6B" rx="3" />
      <text x="165" y="87" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FFFFFF">
        5G网络
      </text>
      
      <!-- 技术参数表格 -->
      <rect x="20" y="120" width="500" height="100" fill="#F5F5F5" stroke="#CCCCCC" stroke-width="1" />
      <line x1="20" y1="140" x2="520" y2="140" stroke="#CCCCCC" stroke-width="1" />
      <line x1="20" y1="160" x2="520" y2="160" stroke="#CCCCCC" stroke-width="1" />
      <line x1="20" y1="180" x2="520" y2="180" stroke="#CCCCCC" stroke-width="1" />
      <line x1="20" y1="200" x2="520" y2="200" stroke="#CCCCCC" stroke-width="1" />
      
      <text x="30" y="135" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333">
        CPU: Intel Xeon E5-2680 v4 @ 2.4GHz
      </text>
      <text x="30" y="155" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333">
        内存: 128GB DDR4 ECC
      </text>
      <text x="30" y="175" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333">
        存储: 2TB NVMe SSD RAID 10
      </text>
      <text x="30" y="195" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333">
        网络: 10Gbps 光纤接口
      </text>
    </g>
    
    <!-- 特征标签 -->
    <g transform="translate(50, 370)">
      <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#FF6B6B">
        特征：
      </text>
      <text x="70" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
        客户看不懂、价值说不清、信任建不立
      </text>
    </g>
  </g>
  
  <!-- 右侧：业务蓝图式方案 -->
  <g transform="translate(1070, 220)">
    <rect x="0" y="0" width="650" height="450" fill="#4CAF50" rx="15" opacity="0.1" />
    <text x="325" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#4CAF50">
      "业务蓝图"式方案
    </text>
    <text x="325" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#666666">
      (真解决方案)
    </text>
    
    <!-- 简洁的业务流程图 -->
    <g transform="translate(50, 100)">
      <rect x="0" y="0" width="550" height="250" fill="#FFFFFF" stroke="#4CAF50" stroke-width="3" rx="10" />
      
      <!-- 业务流程 -->
      <g transform="translate(50, 50)">
        <!-- 现状 -->
        <rect x="0" y="0" width="120" height="60" fill="#FF6B6B" rx="10" opacity="0.2" />
        <text x="60" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">
          现状
        </text>
        <text x="60" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
          效率低下
        </text>
        <text x="60" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
          成本高昂
        </text>
        
        <!-- 箭头 -->
        <path d="M 130 30 L 170 30" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
        
        <!-- 解决方案 -->
        <rect x="180" y="0" width="120" height="60" fill="#2196F3" rx="10" opacity="0.2" />
        <text x="240" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">
          解决方案
        </text>
        <text x="240" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
          智能化改造
        </text>
        <text x="240" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
          流程优化
        </text>
        
        <!-- 箭头 -->
        <path d="M 310 30 L 350 30" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
        
        <!-- 未来愿景 -->
        <rect x="360" y="0" width="120" height="60" fill="#4CAF50" rx="10" opacity="0.2" />
        <text x="420" y="20" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">
          未来愿景
        </text>
        <text x="420" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
          效率提升50%
        </text>
        <text x="420" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
          成本降低30%
        </text>
      </g>
      
      <!-- 三个核心问题 -->
      <g transform="translate(50, 130)">
        <text x="0" y="20" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" font-weight="bold" fill="#4CAF50">
          回答三个核心问题：
        </text>
        <text x="20" y="45" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
          • 我们现在在哪？(现状与痛点)
        </text>
        <text x="20" y="65" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
          • 我们要去哪？(未来与愿景)
        </text>
        <text x="20" y="85" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
          • 我们如何到达？(路径与支撑)
        </text>
      </g>
    </g>
    
    <!-- 特征标签 -->
    <g transform="translate(50, 370)">
      <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#4CAF50">
        特征：
      </text>
      <text x="70" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
        清晰易懂、价值明确、建立信任
      </text>
    </g>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#F5A623" />
    </marker>
  </defs>
  
  <!-- VS -->
  <text x="960" y="450" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48px" font-weight="bold" fill="#F5A623">
    VS
  </text>
  
  <!-- 底部强调 -->
  <rect x="200" y="720" width="1520" height="120" fill="#005A9E" rx="15" />
  <text x="960" y="760" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    那份"产品大礼包"让我们感到困惑和疲惫
  </text>
  <text x="960" y="800" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    而那份"业务蓝图"让我们感到清晰和向往
  </text>
  
  <!-- 困惑图标 -->
  <g transform="translate(100, 400)">
    <circle cx="40" cy="40" r="35" fill="#FF6B6B" opacity="0.2" />
    <text x="40" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FF6B6B">
      ?
    </text>
  </g>
  
  <!-- 清晰图标 -->
  <g transform="translate(1700, 400)">
    <circle cx="40" cy="40" r="35" fill="#4CAF50" opacity="0.2" />
    <text x="40" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#4CAF50">
      ✓
    </text>
  </g>
</svg>
