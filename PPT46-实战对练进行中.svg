<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="70px" font-weight="bold" fill="#005A9E">
    实战对练进行中...
  </text>
  
  <!-- 学员角色扮演场景 -->
  <g transform="translate(400, 250)">
    <!-- 小组1 -->
    <g transform="translate(0, 0)">
      <rect x="0" y="0" width="300" height="200" fill="#4CAF50" rx="15" opacity="0.1" />
      
      <!-- 顾问 -->
      <g transform="translate(50, 50)">
        <circle cx="25" cy="25" r="20" fill="#4CAF50" />
        <rect x="15" y="45" width="20" height="35" fill="#005A9E" rx="3" />
        <circle cx="20" cy="20" r="2" fill="#FFFFFF" />
        <circle cx="30" cy="20" r="2" fill="#FFFFFF" />
        <path d="M 20 30 Q 25 35 30 30" stroke="#FFFFFF" stroke-width="2" fill="none" />
      </g>
      
      <!-- 客户 -->
      <g transform="translate(150, 50)">
        <circle cx="25" cy="25" r="20" fill="#FF6B6B" />
        <rect x="15" y="45" width="20" height="35" fill="#333333" rx="3" />
        <circle cx="20" cy="20" r="2" fill="#FFFFFF" />
        <circle cx="30" cy="20" r="2" fill="#FFFFFF" />
        <path d="M 20 30 Q 25 25 30 30" stroke="#FFFFFF" stroke-width="2" fill="none" />
      </g>
      
      <!-- 观察员 -->
      <g transform="translate(100, 120)">
        <circle cx="25" cy="25" r="20" fill="#2196F3" />
        <rect x="15" y="45" width="20" height="35" fill="#005A9E" rx="3" />
        <rect x="45" y="35" width="15" height="20" fill="#FFFFFF" stroke="#2196F3" stroke-width="1" rx="2" />
      </g>
      
      <text x="150" y="190" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333" font-weight="bold">
        小组A
      </text>
    </g>
    
    <!-- 小组2 -->
    <g transform="translate(400, 0)">
      <rect x="0" y="0" width="300" height="200" fill="#2196F3" rx="15" opacity="0.1" />
      
      <!-- 类似的人物配置 -->
      <g transform="translate(50, 50)">
        <circle cx="25" cy="25" r="20" fill="#4CAF50" />
        <rect x="15" y="45" width="20" height="35" fill="#005A9E" rx="3" />
      </g>
      
      <g transform="translate(150, 50)">
        <circle cx="25" cy="25" r="20" fill="#FF6B6B" />
        <rect x="15" y="45" width="20" height="35" fill="#333333" rx="3" />
      </g>
      
      <g transform="translate(100, 120)">
        <circle cx="25" cy="25" r="20" fill="#2196F3" />
        <rect x="15" y="45" width="20" height="35" fill="#005A9E" rx="3" />
        <rect x="45" y="35" width="15" height="20" fill="#FFFFFF" stroke="#2196F3" stroke-width="1" rx="2" />
      </g>
      
      <text x="150" y="190" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333" font-weight="bold">
        小组B
      </text>
    </g>
    
    <!-- 小组3 -->
    <g transform="translate(800, 0)">
      <rect x="0" y="0" width="300" height="200" fill="#9C27B0" rx="15" opacity="0.1" />
      
      <!-- 类似的人物配置 -->
      <g transform="translate(50, 50)">
        <circle cx="25" cy="25" r="20" fill="#4CAF50" />
        <rect x="15" y="45" width="20" height="35" fill="#005A9E" rx="3" />
      </g>
      
      <g transform="translate(150, 50)">
        <circle cx="25" cy="25" r="20" fill="#FF6B6B" />
        <rect x="15" y="45" width="20" height="35" fill="#333333" rx="3" />
      </g>
      
      <g transform="translate(100, 120)">
        <circle cx="25" cy="25" r="20" fill="#2196F3" />
        <rect x="15" y="45" width="20" height="35" fill="#005A9E" rx="3" />
        <rect x="45" y="35" width="15" height="20" fill="#FFFFFF" stroke="#2196F3" stroke-width="1" rx="2" />
      </g>
      
      <text x="150" y="190" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333" font-weight="bold">
        小组C
      </text>
    </g>
  </g>
  
  <!-- 三轮时间安排 -->
  <g transform="translate(200, 500)">
    <rect x="0" y="0" width="1520" height="200" fill="#F5A623" rx="15" opacity="0.1" />
    
    <!-- Round 1 -->
    <g transform="translate(100, 50)">
      <circle cx="50" cy="50" r="40" fill="#4CAF50" />
      <text x="50" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF" font-weight="bold">Round</text>
      <text x="50" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF" font-weight="bold">1</text>
      
      <text x="50" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333" font-weight="bold">15 min</text>
      <text x="50" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#666666">对练</text>
      <text x="50" y="170" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#666666">+ 5min复盘</text>
    </g>
    
    <!-- Round 2 -->
    <g transform="translate(600, 50)">
      <circle cx="50" cy="50" r="40" fill="#2196F3" />
      <text x="50" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF" font-weight="bold">Round</text>
      <text x="50" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF" font-weight="bold">2</text>
      
      <text x="50" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333" font-weight="bold">15 min</text>
      <text x="50" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#666666">对练</text>
      <text x="50" y="170" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#666666">+ 5min复盘</text>
    </g>
    
    <!-- Round 3 -->
    <g transform="translate(1100, 50)">
      <circle cx="50" cy="50" r="40" fill="#9C27B0" />
      <text x="50" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF" font-weight="bold">Round</text>
      <text x="50" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF" font-weight="bold">3</text>
      
      <text x="50" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333" font-weight="bold">15 min</text>
      <text x="50" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#666666">对练</text>
      <text x="50" y="170" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#666666">+ 5min复盘</text>
    </g>
  </g>
  
  <!-- 倒计时器 -->
  <g transform="translate(1600, 300)">
    <circle cx="60" cy="60" r="50" stroke="#F5A623" stroke-width="6" fill="#FFFFFF" />
    <circle cx="60" cy="60" r="5" fill="#F5A623" />
    
    <!-- 倒计时显示 -->
    <text x="60" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#F5A623" font-weight="bold">
      15:00
    </text>
    <text x="60" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      倒计时
    </text>
    
    <!-- 动态指针 -->
    <line x1="60" y1="60" x2="60" y2="25" stroke="#FF6B6B" stroke-width="3" />
    <line x1="60" y1="60" x2="85" y2="40" stroke="#005A9E" stroke-width="2" />
  </g>
  
  <!-- 底部说明 -->
  <rect x="200" y="750" width="1520" height="120" fill="#005A9E" rx="15" />
  <text x="960" y="790" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    讲师巡场观察，重点关注学员在"I-暗示问题"环节的表现
  </text>
  <text x="960" y="830" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF">
    严格控时，确保三轮演练完整、紧凑地进行
  </text>
  
  <!-- 音乐图标 -->
  <g transform="translate(100, 750)">
    <!-- 音符 -->
    <circle cx="20" cy="40" r="8" fill="#FFD700" />
    <line x1="28" y1="40" x2="28" y2="15" stroke="#333333" stroke-width="3" />
    <path d="M 28 15 Q 35 10 40 15" stroke="#333333" stroke-width="2" fill="none" />
    
    <circle cx="50" cy="35" r="6" fill="#FFD700" />
    <line x1="56" y1="35" x2="56" y2="15" stroke="#333333" stroke-width="2" />
    
    <text x="35" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      背景音乐
    </text>
  </g>
  
  <!-- 观察图标 -->
  <g transform="translate(1700, 750)">
    <!-- 眼睛 -->
    <ellipse cx="35" cy="35" rx="25" ry="15" fill="#FFFFFF" stroke="#005A9E" stroke-width="2" />
    <circle cx="35" cy="35" r="10" fill="#005A9E" />
    <circle cx="35" cy="35" r="5" fill="#FFFFFF" />
    
    <text x="35" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      巡场观察
    </text>
  </g>
</svg>
