<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="66px" font-weight="bold" fill="#005A9E">
    情报的应用：让对话的起点，领先对手1公里
  </text>
  
  <!-- 一级火箭应用话术 -->
  <g transform="translate(200, 220)">
    <rect x="0" y="0" width="1520" height="150" fill="#4CAF50" rx="15" opacity="0.1" />
    <text x="760" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#4CAF50">
      一级火箭应用话术：
    </text>
    
    <!-- 引号 -->
    <text x="50" y="80" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40px" fill="#4CAF50" opacity="0.5">"</text>
    
    <text x="100" y="80" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
      王总，最近我们关注到国家新出台的关于XX行业的数字化转型扶持政策...
    </text>
    <text x="100" y="110" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
      结合贵司的规划...这可能是一个很好的结合点，想听听您的看法。
    </text>
    
    <text x="1400" y="130" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40px" fill="#4CAF50" opacity="0.5">"</text>
    
    <!-- 效果标签 -->
    <rect x="1300" y="50" width="180" height="40" fill="#4CAF50" rx="20" />
    <text x="1390" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" font-weight="bold" fill="#FFFFFF">
      政策研究者身份
    </text>
  </g>
  
  <!-- 二级火箭应用话术 -->
  <g transform="translate(200, 400)">
    <rect x="0" y="0" width="1520" height="150" fill="#2196F3" rx="15" opacity="0.1" />
    <text x="760" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#2196F3">
      二级火箭应用话术：
    </text>
    
    <!-- 引号 -->
    <text x="50" y="80" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40px" fill="#2196F3" opacity="0.5">"</text>
    
    <text x="100" y="80" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
      李总，我仔细拜读了贵司的半年报，特别注意到您提到'提升供应链效率'是核心任务之一...
    </text>
    <text x="100" y="110" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
      我们最近刚好服务了同行业...
    </text>
    
    <text x="1400" y="130" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40px" fill="#2196F3" opacity="0.5">"</text>
    
    <!-- 效果标签 -->
    <rect x="1300" y="50" width="180" height="40" fill="#2196F3" rx="20" />
    <text x="1390" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" font-weight="bold" fill="#FFFFFF">
      做过功课的专业
    </text>
  </g>
  
  <!-- 三级火箭应用话术 -->
  <g transform="translate(200, 580)">
    <rect x="0" y="0" width="1520" height="150" fill="#F5A623" rx="15" opacity="0.1" />
    <text x="760" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#F5A623">
      三级火箭应用话术：
    </text>
    
    <!-- 引号 -->
    <text x="50" y="80" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40px" fill="#F5A623" opacity="0.5">"</text>
    
    <text x="100" y="80" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
      孙总，我了解到您是技术架构的专家...所以今天我没有准备大而全的介绍，
    </text>
    <text x="100" y="110" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
      而是直接画了一张技术集成架构图，我们或许可以从这里开始探讨...
    </text>
    
    <text x="1400" y="130" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40px" fill="#F5A623" opacity="0.5">"</text>
    
    <!-- 效果标签 -->
    <rect x="1300" y="50" width="180" height="40" fill="#F5A623" rx="20" />
    <text x="1390" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" font-weight="bold" fill="#FFFFFF">
      定制化沟通
    </text>
  </g>
  
  <!-- 对比效果 -->
  <g transform="translate(100, 760)">
    <rect x="0" y="0" width="800" height="100" fill="#FF6B6B" rx="15" opacity="0.1" />
    <text x="400" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#FF6B6B">
      传统开场白
    </text>
    <text x="400" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
      "您好，我是XX公司的，今天来给您介绍一下我们的产品..."
    </text>
    <text x="400" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FF6B6B">
      → 瞬间被归类为"推销员"
    </text>
  </g>
  
  <!-- VS -->
  <text x="960" y="820" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#F5A623">
    VS
  </text>
  
  <g transform="translate(1020, 760)">
    <rect x="0" y="0" width="800" height="100" fill="#4CAF50" rx="15" opacity="0.1" />
    <text x="400" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" font-weight="bold" fill="#4CAF50">
      情报驱动开场白
    </text>
    <text x="400" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" fill="#333333">
      基于政策/年报/个人背景的专业洞察开场
    </text>
    <text x="400" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#4CAF50">
      → 瞬间建立"专业顾问"形象
    </text>
  </g>
  
  <!-- 领先距离图示 -->
  <g transform="translate(1600, 300)">
    <!-- 起跑线 -->
    <line x1="0" y1="100" x2="200" y2="100" stroke="#333333" stroke-width="3" />
    <text x="100" y="120" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">起跑线</text>
    
    <!-- 传统销售 -->
    <circle cx="50" cy="80" r="15" fill="#FF6B6B" />
    <text x="50" y="85" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FFFFFF">传统</text>
    
    <!-- 情报驱动销售 -->
    <circle cx="150" cy="50" r="15" fill="#4CAF50" />
    <text x="150" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#FFFFFF">情报</text>
    
    <!-- 领先距离 -->
    <path d="M 65 75 Q 100 40 135 45" stroke="#F5A623" stroke-width="3" fill="none" marker-end="url(#arrowhead)" />
    <text x="100" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#F5A623">1公里领先</text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#F5A623" />
    </marker>
  </defs>
  
  <!-- 底部强调 -->
  <rect x="200" y="900" width="1520" height="80" fill="#005A9E" rx="15" />
  <text x="960" y="950" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    情报的价值：让你的对话起点直接秒杀所有竞争对手
  </text>
</svg>
