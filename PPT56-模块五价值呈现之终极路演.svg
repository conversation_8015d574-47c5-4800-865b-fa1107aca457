<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="58px" font-weight="bold" fill="#005A9E">
    模块五：价值呈现之"终极路演"
  </text>
  
  <!-- 角色转换 -->
  <g transform="translate(200, 250)">
    <rect x="0" y="0" width="1520" height="200" fill="#F5A623" rx="15" opacity="0.1" />
    
    <g transform="translate(200, 50)">
      <text x="0" y="40" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" fill="#333333" font-weight="bold">
        白天，我们是医生和建筑师
      </text>
      <text x="0" y="100" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" fill="#F5A623" font-weight="bold">
        今晚，我们是导演和主演
      </text>
    </g>
  </g>
  
  <!-- 中央舞台场景 -->
  <g transform="translate(600, 500)">
    <!-- 舞台 -->
    <ellipse cx="360" cy="200" rx="300" ry="100" fill="#333333" opacity="0.3" />
    <rect x="60" y="150" width="600" height="100" fill="#8B4513" rx="15" />
    <rect x="80" y="130" width="560" height="30" fill="#D2B48C" rx="8" />
    
    <!-- 聚光灯效果 -->
    <ellipse cx="360" cy="160" rx="200" ry="60" fill="#FFD700" opacity="0.4" />
    <ellipse cx="360" cy="160" rx="150" ry="40" fill="#FFD700" opacity="0.3" />
    <ellipse cx="360" cy="160" rx="100" ry="25" fill="#FFD700" opacity="0.5" />
    
    <!-- 演讲者剪影 -->
    <circle cx="360" cy="120" r="25" fill="#333333" />
    <rect x="345" y="145" width="30" height="40" fill="#333333" rx="6" />
    
    <!-- 演讲手势 -->
    <line x1="330" y1="155" x2="310" y2="140" stroke="#333333" stroke-width="4" />
    <line x1="390" y1="155" x2="410" y2="140" stroke="#333333" stroke-width="4" />
    
    <!-- 光束效果 -->
    <g transform="translate(360, 0)">
      <path d="M -50 0 L -100 160 L 100 160 L 50 0 Z" fill="#FFD700" opacity="0.2" />
      <path d="M -30 0 L -60 160 L 60 160 L 30 0 Z" fill="#FFD700" opacity="0.3" />
    </g>
  </g>
  
  <!-- 左侧：医生和建筑师 -->
  <g transform="translate(100, 500)">
    <rect x="0" y="0" width="400" height="200" fill="#2196F3" rx="15" opacity="0.1" />
    <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#2196F3" font-weight="bold">
      白天的角色
    </text>
    
    <!-- 医生 -->
    <g transform="translate(50, 50)">
      <circle cx="50" cy="50" r="30" fill="#4CAF50" />
      <rect x="30" y="80" width="40" height="50" fill="#FFFFFF" rx="8" />
      
      <!-- 听诊器 -->
      <circle cx="35" cy="45" r="8" fill="#333333" />
      <circle cx="65" cy="45" r="8" fill="#333333" />
      <path d="M 35 53 Q 50 65 65 53" stroke="#333333" stroke-width="3" fill="none" />
      <circle cx="50" cy="90" r="6" fill="#333333" />
      
      <text x="50" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">
        医生
      </text>
    </g>
    
    <!-- 建筑师 -->
    <g transform="translate(250, 50)">
      <circle cx="50" cy="50" r="30" fill="#FF9800" />
      <rect x="30" y="80" width="40" height="50" fill="#005A9E" rx="8" />
      
      <!-- 安全帽 -->
      <ellipse cx="50" cy="35" rx="25" ry="12" fill="#FFD700" />
      <rect x="35" y="30" width="30" height="8" fill="#FFD700" />
      
      <!-- 蓝图 -->
      <rect x="80" y="90" width="30" height="20" fill="#E6F3FF" stroke="#FF9800" stroke-width="2" rx="3" />
      
      <text x="50" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">
        建筑师
      </text>
    </g>
  </g>
  
  <!-- 右侧：导演和主演 -->
  <g transform="translate(1420, 500)">
    <rect x="0" y="0" width="400" height="200" fill="#FF6B6B" rx="15" opacity="0.1" />
    <text x="200" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FF6B6B" font-weight="bold">
      今晚的角色
    </text>
    
    <!-- 导演 -->
    <g transform="translate(50, 50)">
      <circle cx="50" cy="50" r="30" fill="#9C27B0" />
      <rect x="30" y="80" width="40" height="50" fill="#333333" rx="8" />
      
      <!-- 导演帽 -->
      <rect x="35" y="30" width="30" height="15" fill="#333333" rx="3" />
      <rect x="40" y="25" width="20" height="8" fill="#333333" rx="2" />
      
      <!-- 扩音器 -->
      <path d="M 80 60 L 95 55 L 95 65 Z" fill="#FFD700" />
      <rect x="95" y="57" width="15" height="6" fill="#FFD700" rx="3" />
      
      <text x="50" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">
        导演
      </text>
    </g>
    
    <!-- 主演 -->
    <g transform="translate(250, 50)">
      <circle cx="50" cy="50" r="30" fill="#FFD700" />
      <rect x="30" y="80" width="40" height="50" fill="#005A9E" rx="8" />
      
      <!-- 演讲手势 -->
      <line x1="15" y1="90" x2="5" y2="80" stroke="#FFD700" stroke-width="3" />
      <line x1="85" y1="90" x2="95" y2="80" stroke="#FFD700" stroke-width="3" />
      
      <!-- 光环效果 -->
      <circle cx="50" cy="50" r="35" stroke="#FFD700" stroke-width="3" fill="none" opacity="0.5" />
      
      <text x="50" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">
        主演
      </text>
    </g>
  </g>
  
  <!-- 底部激励 -->
  <rect x="200" y="750" width="1520" height="120" fill="#FF6B6B" rx="15" />
  <text x="960" y="790" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    欢迎来到本次理论学习的最后一幕，也是最高潮的一幕！
  </text>
  <text x="960" y="830" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF">
    我们将学习如何让精心设计的方案，开口说话，直击人心！
  </text>
  
  <!-- 聚光灯图标 -->
  <g transform="translate(100, 250)">
    <!-- 聚光灯 -->
    <ellipse cx="40" cy="20" rx="30" ry="10" fill="#FFD700" opacity="0.5" />
    <rect x="35" y="10" width="10" height="15" fill="#333333" rx="2" />
    
    <!-- 光束 -->
    <path d="M 40 25 L 20 80 L 60 80 Z" fill="#FFD700" opacity="0.3" />
    
    <text x="40" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      舞台聚光
    </text>
  </g>
  
  <!-- 时间图标 -->
  <g transform="translate(1700, 250)">
    <circle cx="40" cy="40" r="35" stroke="#F5A623" stroke-width="4" fill="#FFFFFF" />
    <circle cx="40" cy="40" r="3" fill="#F5A623" />
    
    <!-- 时针指向7点 -->
    <line x1="40" y1="40" x2="40" y2="20" stroke="#F5A623" stroke-width="3" />
    <line x1="40" y1="40" x2="25" y2="55" stroke="#005A9E" stroke-width="2" />
    
    <text x="40" y="90" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" font-weight="bold" fill="#F5A623">
      19:00
    </text>
  </g>
</svg>
