<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 - 漆黑 -->
  <rect width="100%" height="100%" fill="#000000" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 聚光灯效果 -->
  <defs>
    <radialGradient id="spotlight" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#FFFFFF;stop-opacity:0.8" />
      <stop offset="70%" style="stop-color:#FFFFFF;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#FFFFFF;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- 聚光灯圆形 -->
  <circle cx="960" cy="540" r="400" fill="url(#spotlight)" />
  
  <!-- 主标题 -->
  <text x="960" y="200" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48px" font-weight="bold" fill="#FFFFFF">
    真相只有一个
  </text>
  
  <!-- 核心词汇 - 诊断 -->
  <g transform="translate(960, 540)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="180px" font-weight="bold" fill="#FF6B6B">
      诊
    </text>
    <text x="0" y="200" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="180px" font-weight="bold" fill="#FF6B6B">
      断
    </text>
  </g>
  
  <!-- 光束效果 -->
  <g transform="translate(960, 100)">
    <path d="M -200 0 L -100 400 L 100 400 L 200 0 Z" fill="#FFD700" opacity="0.2" />
    <path d="M -150 0 L -75 400 L 75 400 L 150 0 Z" fill="#FFFFFF" opacity="0.3" />
    <path d="M -100 0 L -50 400 L 50 400 L 100 0 Z" fill="#FFD700" opacity="0.4" />
  </g>
  
  <!-- 底部金句 -->
  <g transform="translate(200, 800)">
    <rect x="0" y="0" width="1520" height="150" fill="#1A237E" rx="15" opacity="0.8" />
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
      所有的努力——漂亮的方案、殷勤的关系、灵活的报价，
    </text>
    <text x="760" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
      都构建在了一个错误的、想当然的"地基"之上
    </text>
    <text x="760" y="120" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FFD700">
      一次错误的诊断，会让所有正确的执行都变得毫无意义
    </text>
  </g>
  
  <!-- 侧边光效 -->
  <g transform="translate(100, 300)">
    <path d="M 0 0 L 50 200 L -50 200 Z" fill="#4A90E2" opacity="0.3" />
    <path d="M 0 0 L 30 150 L -30 150 Z" fill="#FFFFFF" opacity="0.5" />
  </g>
  
  <g transform="translate(1750, 400)">
    <path d="M 0 0 L 50 200 L -50 200 Z" fill="#9C27B0" opacity="0.3" />
    <path d="M 0 0 L 30 150 L -30 150 Z" fill="#FFFFFF" opacity="0.5" />
  </g>
  
  <!-- 明日预告文字 -->
  <g transform="translate(200, 980)">
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#4A90E2">
      明天上午：学习结构化诊断工具——SPIN场景化提问法
    </text>
    <text x="760" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#87CEEB">
      成为一名真正顾问的"听诊器"
    </text>
  </g>
  
  <!-- 神秘符号 -->
  <g transform="translate(200, 400)">
    <circle cx="30" cy="30" r="25" stroke="#FFD700" stroke-width="3" fill="none" opacity="0.6" />
    <text x="30" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#FFD700">
      !
    </text>
  </g>
  
  <g transform="translate(1600, 600)">
    <circle cx="30" cy="30" r="25" stroke="#FF6B6B" stroke-width="3" fill="none" opacity="0.6" />
    <text x="30" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#FF6B6B">
      ?
    </text>
  </g>
  
  <!-- 粒子效果 -->
  <circle cx="300" cy="200" r="3" fill="#FFD700" opacity="0.8" />
  <circle cx="1500" cy="300" r="2" fill="#4A90E2" opacity="0.6" />
  <circle cx="400" cy="800" r="4" fill="#FF6B6B" opacity="0.7" />
  <circle cx="1600" cy="200" r="2" fill="#FFFFFF" opacity="0.5" />
  <circle cx="200" cy="600" r="3" fill="#9C27B0" opacity="0.6" />
  <circle cx="1700" cy="800" r="2" fill="#4CAF50" opacity="0.7" />
</svg>
