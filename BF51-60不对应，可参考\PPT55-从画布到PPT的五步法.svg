<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64px" font-weight="bold" fill="#005A9E">
    从画布到PPT的"五步法"
  </text>
  <text x="960" y="170" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#666666">
    Canvas to Presentation: 5-Step Method
  </text>
  
  <!-- 五步法流程 -->
  <g transform="translate(200, 220)">
    <!-- 步骤1 -->
    <g transform="translate(0, 0)">
      <rect x="0" y="0" width="280" height="150" fill="#4CAF50" rx="15" opacity="0.1" />
      <circle cx="140" cy="40" r="25" fill="#4CAF50" />
      <text x="140" y="48" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF" font-weight="bold">1</text>
      
      <text x="140" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" font-weight="bold" fill="#4CAF50">
        开场：痛点共鸣
      </text>
      <text x="140" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        基于"现状"模块
      </text>
      <text x="140" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        让客户产生共鸣
      </text>
    </g>
    
    <!-- 箭头 -->
    <path d="M 300 75 L 340 75" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
    
    <!-- 步骤2 -->
    <g transform="translate(360, 0)">
      <rect x="0" y="0" width="280" height="150" fill="#2196F3" rx="15" opacity="0.1" />
      <circle cx="140" cy="40" r="25" fill="#2196F3" />
      <text x="140" y="48" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF" font-weight="bold">2</text>
      
      <text x="140" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" font-weight="bold" fill="#2196F3">
        愿景：美好未来
      </text>
      <text x="140" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        基于"愿景"模块
      </text>
      <text x="140" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        描绘理想状态
      </text>
    </g>
    
    <!-- 箭头 -->
    <path d="M 660 75 L 700 75" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
    
    <!-- 步骤3 -->
    <g transform="translate(720, 0)">
      <rect x="0" y="0" width="280" height="150" fill="#FF9800" rx="15" opacity="0.1" />
      <circle cx="140" cy="40" r="25" fill="#FF9800" />
      <text x="140" y="48" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF" font-weight="bold">3</text>
      
      <text x="140" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" font-weight="bold" fill="#FF9800">
        价值：核心承诺
      </text>
      <text x="140" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        基于"价值主张"模块
      </text>
      <text x="140" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        明确价值承诺
      </text>
    </g>
    
    <!-- 箭头 -->
    <path d="M 1020 75 L 1060 75" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
    
    <!-- 步骤4 -->
    <g transform="translate(1080, 0)">
      <rect x="0" y="0" width="280" height="150" fill="#9C27B0" rx="15" opacity="0.1" />
      <circle cx="140" cy="40" r="25" fill="#9C27B0" />
      <text x="140" y="48" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF" font-weight="bold">4</text>
      
      <text x="140" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" font-weight="bold" fill="#9C27B0">
        方案：如何实现
      </text>
      <text x="140" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        整合"路径+技术+服务"
      </text>
      <text x="140" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        展现实现能力
      </text>
    </g>
    
    <!-- 换行箭头 -->
    <path d="M 1240 170 Q 1280 200 1240 230 Q 1200 260 160 260 Q 120 260 160 230" stroke="#F5A623" stroke-width="4" fill="none" marker-end="url(#arrowhead)" />
    
    <!-- 步骤5 -->
    <g transform="translate(440, 280)">
      <rect x="0" y="0" width="280" height="150" fill="#607D8B" rx="15" opacity="0.1" />
      <circle cx="140" cy="40" r="25" fill="#607D8B" />
      <text x="140" y="48" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF" font-weight="bold">5</text>
      
      <text x="140" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" font-weight="bold" fill="#607D8B">
        回报：投资价值
      </text>
      <text x="140" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        基于"ROI"模块
      </text>
      <text x="140" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333">
        量化投资回报
      </text>
    </g>
  </g>
  
  <!-- 核心逻辑说明 -->
  <g transform="translate(200, 500)">
    <rect x="0" y="0" width="1520" height="200" fill="#005A9E" rx="15" opacity="0.1" />
    <text x="760" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#005A9E">
      五步法的核心逻辑
    </text>
    
    <g transform="translate(100, 60)">
      <text x="0" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" fill="#333333">
        <tspan font-weight="bold" fill="#4CAF50">第1步：</tspan>让客户感受到痛苦（基于现状诊断）
      </text>
      <text x="0" y="60" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" fill="#333333">
        <tspan font-weight="bold" fill="#2196F3">第2步：</tspan>让客户看到希望（描绘美好愿景）
      </text>
      <text x="0" y="90" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" fill="#333333">
        <tspan font-weight="bold" fill="#FF9800">第3步：</tspan>让客户相信价值（明确核心承诺）
      </text>
      <text x="0" y="120" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" fill="#333333">
        <tspan font-weight="bold" fill="#9C27B0">第4步：</tspan>让客户相信能力（展现实现路径）
      </text>
      <text x="0" y="150" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" fill="#333333">
        <tspan font-weight="bold" fill="#607D8B">第5步：</tspan>让客户相信回报（量化投资价值）
      </text>
    </g>
  </g>
  
  <!-- 转换示例 -->
  <g transform="translate(200, 730)">
    <rect x="0" y="0" width="1520" height="120" fill="#F5A623" rx="15" opacity="0.1" />
    <text x="760" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#F5A623">
      转换要点
    </text>
    
    <g transform="translate(100, 50)">
      <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        • 每一页PPT都要有明确的"说服目标"
      </text>
      <text x="500" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        • 用故事化的语言，而非技术化的描述
      </text>
      
      <text x="0" y="55" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        • 每个模块都要回答客户心中的疑问
      </text>
      <text x="500" y="55" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        • 数据要有说服力，逻辑要有穿透力
      </text>
    </g>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#F5A623" />
    </marker>
  </defs>
  
  <!-- 底部金句 -->
  <rect x="200" y="880" width="1520" height="120" fill="#4CAF50" rx="15" />
  <text x="960" y="920" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    记住：我们不是在做技术展示，我们是在讲一个关于客户成功的故事
  </text>
  <text x="960" y="960" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF">
    每一页都要让客户更加相信：这个投资值得！
  </text>
  
  <!-- 画布图标 -->
  <g transform="translate(100, 220)">
    <rect x="0" y="20" width="60" height="40" fill="#E6F3FF" stroke="#005A9E" stroke-width="2" rx="5" />
    <rect x="10" y="30" width="15" height="10" fill="#4CAF50" rx="2" />
    <rect x="30" y="30" width="15" height="10" fill="#2196F3" rx="2" />
    <rect x="10" y="45" width="15" height="10" fill="#FF9800" rx="2" />
    <rect x="30" y="45" width="15" height="10" fill="#9C27B0" rx="2" />
    
    <text x="30" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      画布
    </text>
  </g>
  
  <!-- PPT图标 -->
  <g transform="translate(1700, 500)">
    <rect x="0" y="20" width="60" height="40" fill="#FFE6E6" stroke="#FF6B6B" stroke-width="2" rx="5" />
    <rect x="10" y="30" width="40" height="5" fill="#FF6B6B" rx="1" />
    <rect x="10" y="40" width="25" height="3" fill="#FF6B6B" rx="1" />
    <rect x="10" y="47" width="30" height="3" fill="#FF6B6B" rx="1" />
    <rect x="10" y="54" width="20" height="3" fill="#FF6B6B" rx="1" />
    
    <text x="30" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      PPT
    </text>
  </g>
</svg>
