<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="160" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64px" font-weight="bold" fill="#005A9E">
    "方案架构师"共创工作坊
  </text>
  
  <!-- 任务说明 -->
  <g transform="translate(200, 240)">
    <rect x="0" y="0" width="1520" height="200" fill="#F5A623" rx="15" opacity="0.1" />
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#F5A623">
      任务 (75分钟)
    </text>
    
    <g transform="translate(100, 80)">
      <text x="0" y="30" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#333333">
        • 沿用上午SPIN演练的客户案例
      </text>
      <text x="0" y="70" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#333333">
        • 以小组为单位，为该客户填写一份《增长药方画布》
      </text>
      <text x="0" y="110" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#333333">
        • 将成果呈现在A1大白纸上
      </text>
    </g>
  </g>
  
  <!-- 目标 -->
  <g transform="translate(200, 480)">
    <rect x="0" y="0" width="1520" height="120" fill="#4CAF50" rx="15" opacity="0.1" />
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#4CAF50">
      目标
    </text>
    <text x="760" y="85" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" fill="#333333">
      完成从"诊断"到"开方"的闭环
    </text>
  </g>
  
  <!-- 工作坊场景展示 -->
  <g transform="translate(200, 640)">
    <!-- 小组1 -->
    <g transform="translate(0, 0)">
      <rect x="0" y="0" width="450" height="200" fill="#2196F3" rx="15" opacity="0.1" />
      
      <!-- A1白纸 -->
      <rect x="50" y="30" width="350" height="120" fill="#FFFFFF" stroke="#2196F3" stroke-width="3" rx="8" />
      
      <!-- 画布四象限示意 -->
      <line x1="225" y1="30" x2="225" y2="150" stroke="#2196F3" stroke-width="2" />
      <line x1="50" y1="90" x2="400" y2="90" stroke="#2196F3" stroke-width="2" />
      
      <!-- 象限标识 -->
      <text x="137.5" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FF6B6B" font-weight="bold">
        痛点
      </text>
      <text x="312.5" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#4CAF50" font-weight="bold">
        方案
      </text>
      <text x="137.5" y="125" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#9C27B0" font-weight="bold">
        根因
      </text>
      <text x="312.5" y="125" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#2196F3" font-weight="bold">
        价值
      </text>
      
      <!-- 团队成员 -->
      <circle cx="100" cy="170" r="12" fill="#4CAF50" />
      <circle cx="150" cy="170" r="12" fill="#FF9800" />
      <circle cx="200" cy="170" r="12" fill="#9C27B0" />
      <circle cx="250" cy="170" r="12" fill="#2196F3" />
      
      <text x="225" y="195" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">
        小组A
      </text>
    </g>
    
    <!-- 小组2 -->
    <g transform="translate(535, 0)">
      <rect x="0" y="0" width="450" height="200" fill="#FF9800" rx="15" opacity="0.1" />
      
      <!-- A1白纸 -->
      <rect x="50" y="30" width="350" height="120" fill="#FFFFFF" stroke="#FF9800" stroke-width="3" rx="8" />
      
      <!-- 画布四象限示意 -->
      <line x1="225" y1="30" x2="225" y2="150" stroke="#FF9800" stroke-width="2" />
      <line x1="50" y1="90" x2="400" y2="90" stroke="#FF9800" stroke-width="2" />
      
      <!-- 象限标识 -->
      <text x="137.5" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FF6B6B" font-weight="bold">
        痛点
      </text>
      <text x="312.5" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#4CAF50" font-weight="bold">
        方案
      </text>
      <text x="137.5" y="125" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#9C27B0" font-weight="bold">
        根因
      </text>
      <text x="312.5" y="125" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#2196F3" font-weight="bold">
        价值
      </text>
      
      <!-- 团队成员 -->
      <circle cx="100" cy="170" r="12" fill="#4CAF50" />
      <circle cx="150" cy="170" r="12" fill="#FF9800" />
      <circle cx="200" cy="170" r="12" fill="#9C27B0" />
      <circle cx="250" cy="170" r="12" fill="#2196F3" />
      
      <text x="225" y="195" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">
        小组B
      </text>
    </g>
    
    <!-- 小组3 -->
    <g transform="translate(1070, 0)">
      <rect x="0" y="0" width="450" height="200" fill="#9C27B0" rx="15" opacity="0.1" />
      
      <!-- A1白纸 -->
      <rect x="50" y="30" width="350" height="120" fill="#FFFFFF" stroke="#9C27B0" stroke-width="3" rx="8" />
      
      <!-- 画布四象限示意 -->
      <line x1="225" y1="30" x2="225" y2="150" stroke="#9C27B0" stroke-width="2" />
      <line x1="50" y1="90" x2="400" y2="90" stroke="#9C27B0" stroke-width="2" />
      
      <!-- 象限标识 -->
      <text x="137.5" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FF6B6B" font-weight="bold">
        痛点
      </text>
      <text x="312.5" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#4CAF50" font-weight="bold">
        方案
      </text>
      <text x="137.5" y="125" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#9C27B0" font-weight="bold">
        根因
      </text>
      <text x="312.5" y="125" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#2196F3" font-weight="bold">
        价值
      </text>
      
      <!-- 团队成员 -->
      <circle cx="100" cy="170" r="12" fill="#4CAF50" />
      <circle cx="150" cy="170" r="12" fill="#FF9800" />
      <circle cx="200" cy="170" r="12" fill="#9C27B0" />
      <circle cx="250" cy="170" r="12" fill="#2196F3" />
      
      <text x="225" y="195" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">
        小组C
      </text>
    </g>
  </g>
  
  <!-- 底部激励 -->
  <rect x="200" y="880" width="1520" height="120" fill="#005A9E" rx="15" />
  <text x="960" y="920" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    理论学习结束，现在是"建筑师"们大展身手的时间
  </text>
  <text x="960" y="960" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF">
    从"诊断"到"开方"的完整闭环等待着我们去完成
  </text>
  
  <!-- 时钟图标 -->
  <g transform="translate(100, 240)">
    <circle cx="40" cy="40" r="35" stroke="#F5A623" stroke-width="4" fill="#FFFFFF" />
    <circle cx="40" cy="40" r="3" fill="#F5A623" />
    
    <!-- 时针指向1点15分 -->
    <line x1="40" y1="40" x2="40" y2="20" stroke="#F5A623" stroke-width="3" />
    <line x1="40" y1="40" x2="55" y2="25" stroke="#005A9E" stroke-width="2" />
    
    <text x="40" y="90" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" font-weight="bold" fill="#F5A623">
      75分钟
    </text>
  </g>
  
  <!-- 建筑师图标 -->
  <g transform="translate(1700, 240)">
    <!-- 建筑师 -->
    <circle cx="40" cy="40" r="30" fill="#2196F3" />
    <rect x="25" y="70" width="30" height="40" fill="#005A9E" rx="6" />
    
    <!-- 安全帽 -->
    <ellipse cx="40" cy="25" rx="25" ry="12" fill="#FFD700" />
    <rect x="25" y="20" width="30" height="8" fill="#FFD700" />
    
    <!-- 蓝图 -->
    <rect x="80" y="60" width="40" height="30" fill="#E6F3FF" stroke="#2196F3" stroke-width="2" rx="3" />
    <line x1="85" y1="68" x2="115" y2="68" stroke="#2196F3" stroke-width="1" />
    <line x1="85" y1="75" x2="105" y2="75" stroke="#2196F3" stroke-width="1" />
    <line x1="85" y1="82" x2="110" y2="82" stroke="#2196F3" stroke-width="1" />
    
    <text x="40" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      方案架构师
    </text>
  </g>
</svg>
