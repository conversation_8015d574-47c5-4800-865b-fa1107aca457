<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="66px" font-weight="bold" fill="#005A9E">
    提问的革命：从"询问需求"到"共同探寻"
  </text>
  
  <!-- 传统模式 -->
  <g transform="translate(200, 250)">
    <rect x="0" y="0" width="600" height="300" fill="#FF6B6B" rx="15" opacity="0.1" />
    <text x="300" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#FF6B6B">
      传统模式 (询问)
    </text>
    
    <!-- 问题示例 -->
    <g transform="translate(50, 80)">
      <rect x="0" y="0" width="500" height="60" fill="#FFFFFF" stroke="#FF6B6B" stroke-width="2" rx="10" />
      <text x="250" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" fill="#333333">
        "您有什么需求？"
      </text>
    </g>
    
    <!-- 销售人员图标 -->
    <g transform="translate(100, 160)">
      <!-- 销售人员 -->
      <circle cx="50" cy="40" r="30" fill="#FF6B6B" />
      <rect x="35" y="70" width="30" height="50" fill="#333333" rx="5" />
      
      <!-- 面部特征 -->
      <circle cx="42" cy="35" r="3" fill="#FFFFFF" />
      <circle cx="58" cy="35" r="3" fill="#FFFFFF" />
      <path d="M 42 50 Q 50 45 58 50" stroke="#FFFFFF" stroke-width="2" fill="none" />
      
      <!-- 清单 -->
      <rect x="85" y="80" width="25" height="35" fill="#FFFFFF" stroke="#333333" stroke-width="1" rx="2" />
      <line x1="88" y1="85" x2="107" y2="85" stroke="#333333" stroke-width="1" />
      <line x1="88" y1="90" x2="105" y2="90" stroke="#333333" stroke-width="1" />
      <line x1="88" y1="95" x2="102" y2="95" stroke="#333333" stroke-width="1" />
      <line x1="88" y1="100" x2="106" y2="100" stroke="#333333" stroke-width="1" />
      
      <!-- 勾选 -->
      <path d="M 90 87 L 92 89 L 95 86" stroke="#FF6B6B" stroke-width="2" fill="none" />
      <path d="M 90 97 L 92 99 L 95 96" stroke="#FF6B6B" stroke-width="2" fill="none" />
      
      <text x="50" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FF6B6B" font-weight="bold">
        需求盘点员
      </text>
    </g>
    
    <!-- 客户困惑 -->
    <g transform="translate(350, 160)">
      <circle cx="50" cy="40" r="30" fill="#CCCCCC" />
      <rect x="35" y="70" width="30" height="50" fill="#666666" rx="5" />
      
      <!-- 困惑表情 -->
      <circle cx="42" cy="35" r="3" fill="#FFFFFF" />
      <circle cx="58" cy="35" r="3" fill="#FFFFFF" />
      <path d="M 42 50 Q 50 55 58 50" stroke="#FFFFFF" stroke-width="2" fill="none" />
      
      <!-- 问号 -->
      <circle cx="80" cy="30" r="15" fill="#FFFFFF" stroke="#CCCCCC" stroke-width="2" />
      <text x="80" y="37" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#CCCCCC" font-weight="bold">
        ?
      </text>
      
      <text x="50" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#CCCCCC">
        困惑的客户
      </text>
    </g>
  </g>
  
  <!-- VS -->
  <text x="960" y="420" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48px" font-weight="bold" fill="#F5A623">
    VS
  </text>
  
  <!-- 顾问模式 -->
  <g transform="translate(1120, 250)">
    <rect x="0" y="0" width="600" height="300" fill="#4CAF50" rx="15" opacity="0.1" />
    <text x="300" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#4CAF50">
      顾问模式 (探寻)
    </text>
    
    <!-- 问题示例 -->
    <g transform="translate(50, 80)">
      <rect x="0" y="0" width="500" height="60" fill="#FFFFFF" stroke="#4CAF50" stroke-width="2" rx="10" />
      <text x="250" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        "我们一起看看，
      </text>
      <text x="250" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        最大的增长机会在哪？"
      </text>
    </g>
    
    <!-- 顾问与客户并肩 -->
    <g transform="translate(150, 160)">
      <!-- 顾问 -->
      <circle cx="40" cy="40" r="25" fill="#4CAF50" />
      <rect x="27" y="65" width="26" height="45" fill="#005A9E" rx="4" />
      
      <!-- 客户 -->
      <circle cx="90" cy="40" r="25" fill="#2196F3" />
      <rect x="77" y="65" width="26" height="45" fill="#333333" rx="4" />
      
      <!-- 探照灯 -->
      <g transform="translate(120, 70)">
        <rect x="0" y="0" width="15" height="8" fill="#FFD700" rx="2" />
        <path d="M 15 4 L 35 0 L 35 8 Z" fill="#FFD700" opacity="0.6" />
        <path d="M 35 0 L 55 -5 L 55 13 L 35 8 Z" fill="#FFFFFF" opacity="0.4" />
      </g>
      
      <!-- 迷雾 -->
      <ellipse cx="180" cy="75" rx="40" ry="20" fill="#CCCCCC" opacity="0.3" />
      <ellipse cx="200" cy="85" rx="30" ry="15" fill="#CCCCCC" opacity="0.4" />
      
      <text x="65" y="140" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#4CAF50" font-weight="bold">
        探险向导
      </text>
    </g>
  </g>
  
  <!-- 核心转变 -->
  <g transform="translate(200, 600)">
    <rect x="0" y="0" width="1520" height="100" fill="#F5A623" rx="15" opacity="0.1" />
    <text x="760" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="36px" font-weight="bold" fill="#F5A623">
      核心转变：
    </text>
    <text x="760" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" fill="#333333">
      从"需求盘点员"到"探险向导"
    </text>
  </g>
  
  <!-- 扎心真相 -->
  <g transform="translate(200, 750)">
    <rect x="0" y="0" width="1520" height="150" fill="#005A9E" rx="15" />
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
      记住一个扎心的真相：
    </text>
    <text x="760" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FFD700">
      客户只知道自己"痛"，但并不知道自己"需要"什么
    </text>
    <text x="760" y="120" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF">
      一个顾问会说："别担心，我将用我的专业，像一名向导一样，
    </text>
    <text x="760" y="145" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF">
      陪您一起探寻问题的根源和未来的方向。"
    </text>
  </g>
  
  <!-- 革命标识 -->
  <g transform="translate(100, 200)">
    <circle cx="50" cy="50" r="40" fill="#FF6B6B" opacity="0.2" />
    <text x="50" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FF6B6B">
      革命
    </text>
  </g>
  
  <!-- 探索标识 -->
  <g transform="translate(1700, 200)">
    <circle cx="50" cy="50" r="40" fill="#4CAF50" opacity="0.2" />
    <text x="50" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#4CAF50">
      探寻
    </text>
  </g>
</svg>
