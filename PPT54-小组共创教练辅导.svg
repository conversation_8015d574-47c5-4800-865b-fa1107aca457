<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64px" font-weight="bold" fill="#005A9E">
    小组共创 &amp; 教练辅导
  </text>
  
  <!-- 中央共创场景 -->
  <g transform="translate(600, 220)">
    <!-- A1大白纸 -->
    <rect x="0" y="0" width="720" height="480" fill="#FFFFFF" stroke="#005A9E" stroke-width="6" rx="15" />
    
    <!-- 画布四象限 -->
    <line x1="360" y1="0" x2="360" y2="480" stroke="#005A9E" stroke-width="4" />
    <line x1="0" y1="240" x2="720" y2="240" stroke="#005A9E" stroke-width="4" />
    
    <!-- 象限标题 -->
    <text x="180" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FF6B6B" font-weight="bold">
      诊断痛点
    </text>
    <text x="540" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#4CAF50" font-weight="bold">
      方案模块
    </text>
    <text x="180" y="270" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#9C27B0" font-weight="bold">
      归因根源
    </text>
    <text x="540" y="270" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#2196F3" font-weight="bold">
      核心价值
    </text>
    
    <!-- 便利贴模拟 -->
    <!-- 痛点象限 -->
    <rect x="20" y="60" width="120" height="40" fill="#FFE4E1" stroke="#FF6B6B" stroke-width="2" rx="5" />
    <text x="80" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      生产效率低
    </text>
    <text x="80" y="90" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      仅达标60%
    </text>
    
    <rect x="160" y="80" width="120" height="40" fill="#FFE4E1" stroke="#FF6B6B" stroke-width="2" rx="5" />
    <text x="220" y="95" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      质量不稳定
    </text>
    <text x="220" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      次品率8%
    </text>
    
    <!-- 方案象限 -->
    <rect x="400" y="60" width="120" height="40" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="5" />
    <text x="460" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      智能监控
    </text>
    <text x="460" y="90" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      模块
    </text>
    
    <rect x="540" y="80" width="120" height="40" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" rx="5" />
    <text x="600" y="95" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      工艺标准化
    </text>
    <text x="600" y="110" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      模块
    </text>
    
    <!-- 根因象限 -->
    <rect x="20" y="300" width="120" height="40" fill="#F3E5F5" stroke="#9C27B0" stroke-width="2" rx="5" />
    <text x="80" y="315" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      设备老化
    </text>
    <text x="80" y="330" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      缺乏监控
    </text>
    
    <!-- 价值象限 -->
    <rect x="400" y="300" width="120" height="40" fill="#E3F2FD" stroke="#2196F3" stroke-width="2" rx="5" />
    <text x="460" y="315" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      效率提升30%
    </text>
    <text x="460" y="330" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      月增收200万
    </text>
  </g>
  
  <!-- 学员围绕讨论 -->
  <g transform="translate(400, 300)">
    <!-- 学员1 -->
    <circle cx="50" cy="100" r="25" fill="#4CAF50" />
    <rect x="35" y="125" width="30" height="40" fill="#005A9E" rx="6" />
    
    <!-- 学员2 -->
    <circle cx="150" cy="50" r="25" fill="#FF9800" />
    <rect x="135" y="75" width="30" height="40" fill="#333333" rx="6" />
    
    <!-- 学员3 -->
    <circle cx="1070" cy="50" r="25" fill="#9C27B0" />
    <rect x="1055" y="75" width="30" height="40" fill="#005A9E" rx="6" />
    
    <!-- 学员4 -->
    <circle cx="1170" cy="100" r="25" fill="#2196F3" />
    <rect x="1155" y="125" width="30" height="40" fill="#333333" rx="6" />
    
    <!-- 讨论气泡 -->
    <ellipse cx="100" cy="200" rx="60" ry="30" fill="#FFFFFF" stroke="#4CAF50" stroke-width="2" />
    <text x="100" y="195" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      "这个痛点够痛吗？"
    </text>
    <text x="100" y="210" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      "有量化数据支撑吗？"
    </text>
  </g>
  
  <!-- 教练辅导场景 -->
  <g transform="translate(200, 750)">
    <!-- 教练 -->
    <circle cx="100" cy="50" r="30" fill="#FFD700" />
    <rect x="80" y="80" width="40" height="50" fill="#005A9E" rx="8" />
    
    <!-- 教练标识 -->
    <rect x="85" y="85" width="30" height="20" fill="#FFD700" rx="3" />
    <text x="100" y="98" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333" font-weight="bold">
      教练
    </text>
    
    <!-- 教练提问气泡 -->
    <ellipse cx="200" cy="60" rx="120" ry="40" fill="#FFFFFF" stroke="#FFD700" stroke-width="3" />
    <text x="200" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">
      "你们的方案模块，和左边的根源，"
    </text>
    <text x="200" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      "是强对应关系吗？会不会是为了"
    </text>
    <text x="200" y="85" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      "上产品而硬凑的？"
    </text>
    
    <text x="100" y="160" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333" font-weight="bold">
      催化剂 &amp; 挑战者
    </text>
  </g>
  
  <!-- 金句展示 -->
  <rect x="600" y="750" width="1120" height="120" fill="#F5A623" rx="15" />
  <text x="1160" y="790" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#FFFFFF">
    "我们卖的不是钻头，而是墙上的那个洞"
  </text>
  <text x="1160" y="830" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF">
    专注客户价值，而非产品功能
  </text>
  
  <!-- 音乐图标 -->
  <g transform="translate(100, 220)">
    <!-- 音符 -->
    <circle cx="30" cy="60" r="8" fill="#F5A623" />
    <rect x="38" y="30" width="3" height="30" fill="#F5A623" />
    <path d="M 41 30 Q 50 25 55 30 Q 50 35 41 35" fill="#F5A623" />
    
    <circle cx="60" cy="50" r="8" fill="#F5A623" />
    <rect x="68" y="20" width="3" height="30" fill="#F5A623" />
    <path d="M 71 20 Q 80 15 85 20 Q 80 25 71 25" fill="#F5A623" />
    
    <!-- 音波 -->
    <path d="M 90 40 Q 100 35 90 30" stroke="#F5A623" stroke-width="2" fill="none" />
    <path d="M 95 50 Q 110 40 95 30" stroke="#F5A623" stroke-width="2" fill="none" />
    <path d="M 100 60 Q 120 45 100 30" stroke="#F5A623" stroke-width="2" fill="none" />
    
    <text x="60" y="100" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      创造力音乐
    </text>
  </g>
  
  <!-- 计时器 -->
  <g transform="translate(1700, 220)">
    <circle cx="60" cy="60" r="50" stroke="#FF6B6B" stroke-width="6" fill="#FFFFFF" />
    <circle cx="60" cy="60" r="5" fill="#FF6B6B" />
    
    <!-- 倒计时显示 -->
    <text x="60" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FF6B6B" font-weight="bold">
      45:30
    </text>
    <text x="60" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      剩余时间
    </text>
    
    <!-- 动态指针 -->
    <line x1="60" y1="60" x2="60" y2="25" stroke="#FF6B6B" stroke-width="3" />
    <line x1="60" y1="60" x2="85" y2="40" stroke="#005A9E" stroke-width="2" />
  </g>
</svg>
