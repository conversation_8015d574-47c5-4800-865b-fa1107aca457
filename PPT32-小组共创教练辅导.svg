<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="70px" font-weight="bold" fill="#005A9E">
    小组共创 &amp; 教练辅导
  </text>
  
  <!-- 学员协作场景 -->
  <g transform="translate(300, 250)">
    <!-- A1大白纸 -->
    <rect x="200" y="100" width="400" height="300" fill="#FFFFFF" stroke="#005A9E" stroke-width="4" rx="10" />
    
    <!-- 四象限示意 -->
    <line x1="400" y1="110" x2="400" y2="390" stroke="#CCCCCC" stroke-width="2" />
    <line x1="210" y1="250" x2="590" y2="250" stroke="#CCCCCC" stroke-width="2" />
    
    <!-- 象限标签 -->
    <text x="300" y="140" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#4CAF50" font-weight="bold">外部环境</text>
    <text x="500" y="140" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#2196F3" font-weight="bold">内部现状</text>
    <text x="300" y="290" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#F5A623" font-weight="bold">机会切入点</text>
    <text x="500" y="290" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#9C27B0" font-weight="bold">接触策略</text>
    
    <!-- 便利贴 -->
    <rect x="220" y="160" width="40" height="30" fill="#FFD700" rx="3" />
    <rect x="270" y="180" width="40" height="30" fill="#FFD700" rx="3" />
    <rect x="520" y="170" width="40" height="30" fill="#87CEEB" rx="3" />
    <rect x="470" y="190" width="40" height="30" fill="#87CEEB" rx="3" />
    
    <!-- 学员人物 -->
    <!-- 学员1 -->
    <g transform="translate(100, 200)">
      <circle cx="25" cy="25" r="20" fill="#F5A623" />
      <rect x="15" y="45" width="20" height="40" fill="#005A9E" rx="5" />
      <circle cx="20" cy="20" r="3" fill="#FFFFFF" />
      <circle cx="30" cy="20" r="3" fill="#FFFFFF" />
      <path d="M 20 30 Q 25 35 30 30" stroke="#FFFFFF" stroke-width="2" fill="none" />
    </g>
    
    <!-- 学员2 -->
    <g transform="translate(650, 180)">
      <circle cx="25" cy="25" r="20" fill="#F5A623" />
      <rect x="15" y="45" width="20" height="40" fill="#005A9E" rx="5" />
      <circle cx="20" cy="20" r="3" fill="#FFFFFF" />
      <circle cx="30" cy="20" r="3" fill="#FFFFFF" />
      <path d="M 20 30 Q 25 35 30 30" stroke="#FFFFFF" stroke-width="2" fill="none" />
    </g>
    
    <!-- 学员3 -->
    <g transform="translate(350, 450)">
      <circle cx="25" cy="25" r="20" fill="#F5A623" />
      <rect x="15" y="45" width="20" height="40" fill="#005A9E" rx="5" />
      <circle cx="20" cy="20" r="3" fill="#FFFFFF" />
      <circle cx="30" cy="20" r="3" fill="#FFFFFF" />
      <path d="M 20 30 Q 25 35 30 30" stroke="#FFFFFF" stroke-width="2" fill="none" />
    </g>
    
    <!-- 讨论气泡 -->
    <ellipse cx="150" cy="150" rx="40" ry="25" fill="#FFFFFF" stroke="#F5A623" stroke-width="2" />
    <text x="150" y="155" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">讨论中</text>
    
    <ellipse cx="700" cy="130" rx="40" ry="25" fill="#FFFFFF" stroke="#F5A623" stroke-width="2" />
    <text x="700" y="135" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">分析中</text>
  </g>
  
  <!-- 核心金句 -->
  <g transform="translate(200, 600)">
    <rect x="0" y="0" width="1520" height="120" fill="#F5A623" rx="15" opacity="0.1" />
    
    <!-- 引号 -->
    <text x="50" y="60" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="60px" fill="#F5A623" opacity="0.5">"</text>
    
    <text x="760" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="48px" font-weight="bold" fill="#F5A623">
      洞察，藏在信息的连接处与矛盾处
    </text>
    
    <text x="1400" y="100" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="60px" fill="#F5A623" opacity="0.5">"</text>
  </g>
  
  <!-- 教练巡场图标 -->
  <g transform="translate(1200, 300)">
    <!-- 教练人物 -->
    <circle cx="50" cy="30" r="25" fill="#005A9E" />
    <rect x="35" y="55" width="30" height="50" fill="#333333" rx="5" />
    <circle cx="42" cy="25" r="3" fill="#FFFFFF" />
    <circle cx="58" cy="25" r="3" fill="#FFFFFF" />
    <path d="M 42 35 Q 50 40 58 35" stroke="#FFFFFF" stroke-width="2" fill="none" />
    
    <!-- 教练帽 -->
    <ellipse cx="50" cy="15" rx="20" ry="8" fill="#333333" />
    <rect x="40" y="10" width="20" height="8" fill="#333333" />
    
    <!-- 巡场路径 -->
    <path d="M 100 50 Q 150 30 200 50 Q 250 70 300 50" stroke="#005A9E" stroke-width="3" stroke-dasharray="5,5" fill="none" />
    
    <text x="50" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#005A9E" font-weight="bold">
      教练巡场
    </text>
  </g>
  
  <!-- 提问引导图标 -->
  <g transform="translate(1400, 400)">
    <!-- 问号 -->
    <circle cx="40" cy="40" r="35" fill="#F5A623" opacity="0.2" />
    <text x="40" y="55" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="40px" font-weight="bold" fill="#F5A623">
      ?
    </text>
    
    <!-- 引导箭头 -->
    <path d="M 80 40 L 120 40" stroke="#F5A623" stroke-width="4" marker-end="url(#arrowhead)" />
    
    <!-- 灯泡 -->
    <circle cx="150" cy="40" r="20" fill="#FFD700" opacity="0.3" />
    <path d="M 140 30 Q 150 20 160 30 Q 150 40 160 50 Q 150 60 140 50 Q 150 40 140 30" fill="#FFD700" />
    
    <text x="100" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
      提问引导
    </text>
  </g>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#F5A623" />
    </marker>
  </defs>
  
  <!-- 底部时间提示 -->
  <rect x="200" y="750" width="1520" height="80" fill="#005A9E" rx="15" />
  <text x="960" y="800" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    工作坊进行中 - 教练式提问，绝不直接给出答案
  </text>
  
  <!-- 计时器 -->
  <g transform="translate(1600, 850)">
    <circle cx="30" cy="30" r="25" stroke="#FFFFFF" stroke-width="3" fill="none" />
    <text x="30" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#FFFFFF" font-weight="bold">
      60min
    </text>
  </g>
</svg>
