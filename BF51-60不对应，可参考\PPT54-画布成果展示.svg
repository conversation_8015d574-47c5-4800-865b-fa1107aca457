<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="70px" font-weight="bold" fill="#005A9E">
    画布成果展示
  </text>
  
  <!-- 展示规则 -->
  <g transform="translate(200, 220)">
    <rect x="0" y="0" width="1520" height="100" fill="#F5A623" rx="15" opacity="0.1" />
    <text x="760" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#F5A623">
      展示规则：每组5分钟，重点展示业务逻辑与价值主张
    </text>
    <text x="760" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
      其他小组作为"客户"，从客户视角提出质疑与建议
    </text>
  </g>
  
  <!-- 展示场景 -->
  <g transform="translate(200, 350)">
    <!-- 展示台 -->
    <rect x="0" y="0" width="1520" height="300" fill="#005A9E" rx="15" opacity="0.1" />
    
    <!-- 演讲者 -->
    <g transform="translate(100, 50)">
      <circle cx="50" cy="50" r="35" fill="#4CAF50" />
      <rect x="30" y="85" width="40" height="60" fill="#005A9E" rx="8" />
      
      <!-- 面部特征 -->
      <circle cx="40" cy="45" r="4" fill="#FFFFFF" />
      <circle cx="60" cy="45" r="4" fill="#FFFFFF" />
      <path d="M 40 60 Q 50 65 60 60" stroke="#FFFFFF" stroke-width="2" fill="none" />
      
      <!-- 演讲手势 -->
      <line x1="15" y1="100" x2="5" y2="90" stroke="#4CAF50" stroke-width="3" />
      <line x1="85" y1="100" x2="95" y2="90" stroke="#4CAF50" stroke-width="3" />
      
      <!-- 演讲气泡 -->
      <ellipse cx="150" cy="60" rx="80" ry="30" fill="#FFFFFF" stroke="#4CAF50" stroke-width="2" />
      <text x="150" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333" font-weight="bold">
        "我们的方案将帮助客户"
      </text>
      <text x="150" y="70" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        "实现30%的效率提升..."
      </text>
      
      <text x="50" y="170" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#333333" font-weight="bold">
        小组A代表
      </text>
    </g>
    
    <!-- 画布展示 -->
    <g transform="translate(400, 50)">
      <rect x="0" y="0" width="400" height="200" fill="#FFFFFF" stroke="#4CAF50" stroke-width="3" rx="10" />
      
      <!-- 画布内容模拟 -->
      <text x="200" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#4CAF50">
        智能制造转型方案
      </text>
      
      <!-- 七个模块 -->
      <rect x="20" y="40" width="110" height="60" fill="#FF6B6B" rx="5" opacity="0.2" />
      <text x="75" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333" font-weight="bold">
        现状
      </text>
      <text x="75" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#666666">
        生产效率低
      </text>
      <text x="75" y="88" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#666666">
        质量不稳定
      </text>
      
      <rect x="145" y="40" width="110" height="60" fill="#4CAF50" rx="5" opacity="0.2" />
      <text x="200" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333" font-weight="bold">
        愿景
      </text>
      <text x="200" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#666666">
        智能化生产
      </text>
      <text x="200" y="88" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#666666">
        质量可控
      </text>
      
      <rect x="270" y="40" width="110" height="60" fill="#2196F3" rx="5" opacity="0.2" />
      <text x="325" y="60" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333" font-weight="bold">
        价值主张
      </text>
      <text x="325" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#666666">
        效率提升30%
      </text>
      <text x="325" y="88" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#666666">
        成本降低20%
      </text>
      
      <!-- 下排模块 -->
      <rect x="20" y="115" width="85" height="60" fill="#9C27B0" rx="5" opacity="0.2" />
      <text x="62.5" y="135" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333" font-weight="bold">
        实施路径
      </text>
      <text x="62.5" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="9px" fill="#666666">
        分三期实施
      </text>
      
      <rect x="115" y="115" width="85" height="60" fill="#FF9800" rx="5" opacity="0.2" />
      <text x="157.5" y="135" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333" font-weight="bold">
        技术支撑
      </text>
      <text x="157.5" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="9px" fill="#666666">
        IoT+AI平台
      </text>
      
      <rect x="210" y="115" width="85" height="60" fill="#607D8B" rx="5" opacity="0.2" />
      <text x="252.5" y="135" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333" font-weight="bold">
        服务保障
      </text>
      <text x="252.5" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="9px" fill="#666666">
        7×24支持
      </text>
      
      <rect x="305" y="115" width="75" height="60" fill="#4CAF50" rx="5" opacity="0.2" />
      <text x="342.5" y="135" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333" font-weight="bold">
        ROI
      </text>
      <text x="342.5" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="9px" fill="#666666">
        18个月回本
      </text>
    </g>
    
    <!-- 观众席 -->
    <g transform="translate(900, 80)">
      <text x="0" y="0" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="18px" font-weight="bold" fill="#333333">
        观众席（其他小组）
      </text>
      
      <!-- 观众1 -->
      <g transform="translate(50, 20)">
        <circle cx="25" cy="25" r="20" fill="#2196F3" />
        <rect x="15" y="45" width="20" height="30" fill="#333333" rx="3" />
        
        <!-- 举手提问 -->
        <line x1="35" y1="35" x2="45" y2="25" stroke="#2196F3" stroke-width="3" />
        
        <!-- 问题气泡 -->
        <ellipse cx="70" cy="20" rx="35" ry="15" fill="#FFFFFF" stroke="#2196F3" stroke-width="2" />
        <text x="70" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333">
          ROI如何计算？
        </text>
      </g>
      
      <!-- 观众2 -->
      <g transform="translate(200, 20)">
        <circle cx="25" cy="25" r="20" fill="#9C27B0" />
        <rect x="15" y="45" width="20" height="30" fill="#005A9E" rx="3" />
        
        <!-- 思考状态 -->
        <circle cx="20" cy="20" r="2" fill="#FFFFFF" />
        <circle cx="30" cy="20" r="2" fill="#FFFFFF" />
        <path d="M 20 30 Q 25 25 30 30" stroke="#FFFFFF" stroke-width="2" fill="none" />
        
        <!-- 思考气泡 -->
        <ellipse cx="70" cy="20" rx="40" ry="15" fill="#FFFFFF" stroke="#9C27B0" stroke-width="2" />
        <text x="70" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333">
          风险控制措施？
        </text>
      </g>
      
      <!-- 观众3 -->
      <g transform="translate(350, 20)">
        <circle cx="25" cy="25" r="20" fill="#FF9800" />
        <rect x="15" y="45" width="20" height="30" fill="#005A9E" rx="3" />
        
        <!-- 记录状态 -->
        <rect x="45" y="35" width="15" height="20" fill="#FFFFFF" stroke="#FF9800" stroke-width="1" rx="2" />
        <line x1="48" y1="40" x2="57" y2="40" stroke="#FF9800" stroke-width="1" />
        <line x1="48" y1="45" x2="55" y2="45" stroke="#FF9800" stroke-width="1" />
        <line x1="48" y1="50" x2="57" y2="50" stroke="#FF9800" stroke-width="1" />
      </g>
    </g>
  </g>
  
  <!-- 评价标准 -->
  <g transform="translate(200, 680)">
    <rect x="0" y="0" width="1520" height="120" fill="#2196F3" rx="15" opacity="0.1" />
    <text x="760" y="30" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" font-weight="bold" fill="#2196F3">
      评价标准
    </text>
    
    <g transform="translate(100, 50)">
      <text x="0" y="20" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        • 业务逻辑是否清晰完整？
      </text>
      <text x="400" y="20" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        • 价值主张是否令人信服？
      </text>
      <text x="800" y="20" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        • ROI计算是否合理？
      </text>
      
      <text x="0" y="50" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        • 实施路径是否可行？
      </text>
      <text x="400" y="50" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        • 风险控制是否充分？
      </text>
      <text x="800" y="50" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#333333">
        • 客户视角是否到位？
      </text>
    </g>
  </g>
  
  <!-- 底部激励 -->
  <rect x="200" y="830" width="1520" height="120" fill="#4CAF50" rx="15" />
  <text x="960" y="870" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
    这不是一次作业展示，这是一次"投资路演"
  </text>
  <text x="960" y="910" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF">
    你们要说服"投资人"相信：这个方案值得投资！
  </text>
  
  <!-- 计时器 -->
  <g transform="translate(100, 350)">
    <circle cx="40" cy="40" r="35" stroke="#FF6B6B" stroke-width="4" fill="#FFFFFF" />
    <circle cx="40" cy="40" r="3" fill="#FF6B6B" />
    
    <!-- 时针 -->
    <line x1="40" y1="40" x2="40" y2="20" stroke="#FF6B6B" stroke-width="3" />
    <line x1="40" y1="40" x2="55" y2="30" stroke="#005A9E" stroke-width="2" />
    
    <text x="40" y="90" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" font-weight="bold" fill="#FF6B6B">
      5分钟
    </text>
  </g>
  
  <!-- 投资人图标 -->
  <g transform="translate(1700, 350)">
    <!-- 投资人形象 -->
    <circle cx="40" cy="40" r="30" fill="#FFD700" />
    <rect x="25" y="70" width="30" height="40" fill="#333333" rx="6" />
    
    <!-- 西装领带 -->
    <rect x="35" y="75" width="10" height="25" fill="#FF6B6B" rx="2" />
    
    <!-- 面部特征 -->
    <circle cx="32" cy="35" r="3" fill="#333333" />
    <circle cx="48" cy="35" r="3" fill="#333333" />
    <path d="M 32 50 Q 40 55 48 50" stroke="#333333" stroke-width="2" fill="none" />
    
    <!-- 钱袋 -->
    <ellipse cx="70" cy="80" rx="12" ry="15" fill="#4CAF50" />
    <rect x="65" y="70" width="10" height="8" fill="#4CAF50" />
    <text x="70" y="85" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#FFFFFF" font-weight="bold">
      $
    </text>
    
    <text x="40" y="130" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      投资人视角
    </text>
  </g>
</svg>
