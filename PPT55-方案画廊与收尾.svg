<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="#FFFFFF" />
  
  <!-- 装饰弧线 - 左上角 -->
  <path d="M 120 120 Q 200 120 200 200" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 120 160 Q 160 160 160 200" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 装饰弧线 - 右下角 -->
  <path d="M 1800 960 Q 1720 960 1720 880" stroke="#F5A623" stroke-width="8" fill="none" />
  <path d="M 1800 920 Q 1760 920 1760 880" stroke="#F5A623" stroke-width="8" fill="none" />
  
  <!-- 主标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="64px" font-weight="bold" fill="#005A9E">
    "方案画廊"与收尾
  </text>
  
  <!-- 活动说明 -->
  <g transform="translate(200, 220)">
    <rect x="0" y="0" width="1520" height="120" fill="#4CAF50" rx="15" opacity="0.1" />
    <text x="760" y="35" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="32px" font-weight="bold" fill="#4CAF50">
      活动：画廊漫步 (Gallery Walk)
    </text>
    
    <g transform="translate(100, 60)">
      <text x="0" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
        • 将画布张贴在墙上
      </text>
      <text x="400" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
        • 1位"讲解员"
      </text>
      <text x="800" y="25" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#333333">
        • 其他成员"漫步"，用便利贴提问或点赞
      </text>
    </g>
  </g>
  
  <!-- 画廊场景 -->
  <g transform="translate(200, 380)">
    <!-- 墙面背景 -->
    <rect x="0" y="0" width="1520" height="400" fill="#F8F9FA" rx="15" />
    
    <!-- 画布展示区1 -->
    <g transform="translate(50, 50)">
      <rect x="0" y="0" width="300" height="200" fill="#FFFFFF" stroke="#2196F3" stroke-width="4" rx="10" />
      <text x="150" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#2196F3" font-weight="bold">
        小组A方案画布
      </text>
      
      <!-- 四象限简化显示 -->
      <line x1="150" y1="40" x2="150" y2="180" stroke="#2196F3" stroke-width="2" />
      <line x1="20" y1="110" x2="280" y2="110" stroke="#2196F3" stroke-width="2" />
      
      <!-- 讲解员 -->
      <circle cx="150" cy="250" r="20" fill="#2196F3" />
      <rect x="140" y="270" width="20" height="30" fill="#005A9E" rx="4" />
      <text x="150" y="315" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333" font-weight="bold">
        讲解员
      </text>
      
      <!-- 便利贴评论 -->
      <rect x="320" y="60" width="60" height="30" fill="#FFD700" rx="3" />
      <text x="350" y="75" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333">
        很棒的
      </text>
      <text x="350" y="85" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333">
        逻辑！
      </text>
      
      <rect x="320" y="120" width="60" height="30" fill="#FFE4E1" rx="3" />
      <text x="350" y="135" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333">
        价值量化
      </text>
      <text x="350" y="145" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333">
        可以更具体
      </text>
    </g>
    
    <!-- 画布展示区2 -->
    <g transform="translate(450, 50)">
      <rect x="0" y="0" width="300" height="200" fill="#FFFFFF" stroke="#FF9800" stroke-width="4" rx="10" />
      <text x="150" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#FF9800" font-weight="bold">
        小组B方案画布
      </text>
      
      <!-- 四象限简化显示 -->
      <line x1="150" y1="40" x2="150" y2="180" stroke="#FF9800" stroke-width="2" />
      <line x1="20" y1="110" x2="280" y2="110" stroke="#FF9800" stroke-width="2" />
      
      <!-- 讲解员 -->
      <circle cx="150" cy="250" r="20" fill="#FF9800" />
      <rect x="140" y="270" width="20" height="30" fill="#333333" rx="4" />
      <text x="150" y="315" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333" font-weight="bold">
        讲解员
      </text>
      
      <!-- 便利贴评论 -->
      <rect x="320" y="80" width="60" height="30" fill="#E8F5E8" rx="3" />
      <text x="350" y="95" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333">
        根因分析
      </text>
      <text x="350" y="105" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333">
        很深入！
      </text>
    </g>
    
    <!-- 画布展示区3 -->
    <g transform="translate(850, 50)">
      <rect x="0" y="0" width="300" height="200" fill="#FFFFFF" stroke="#9C27B0" stroke-width="4" rx="10" />
      <text x="150" y="25" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="16px" fill="#9C27B0" font-weight="bold">
        小组C方案画布
      </text>
      
      <!-- 四象限简化显示 -->
      <line x1="150" y1="40" x2="150" y2="180" stroke="#9C27B0" stroke-width="2" />
      <line x1="20" y1="110" x2="280" y2="110" stroke="#9C27B0" stroke-width="2" />
      
      <!-- 讲解员 -->
      <circle cx="150" cy="250" r="20" fill="#9C27B0" />
      <rect x="140" y="270" width="20" height="30" fill="#005A9E" rx="4" />
      <text x="150" y="315" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333" font-weight="bold">
        讲解员
      </text>
      
      <!-- 便利贴评论 -->
      <rect x="320" y="100" width="60" height="30" fill="#E3F2FD" rx="3" />
      <text x="350" y="115" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333">
        创新的
      </text>
      <text x="350" y="125" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="10px" fill="#333333">
        视角！
      </text>
    </g>
    
    <!-- 漫步观众 -->
    <g transform="translate(1250, 150)">
      <!-- 观众1 -->
      <circle cx="50" cy="50" r="18" fill="#4CAF50" />
      <rect x="40" y="68" width="20" height="30" fill="#333333" rx="4" />
      
      <!-- 观众2 -->
      <circle cx="100" cy="80" r="18" fill="#2196F3" />
      <rect x="90" y="98" width="20" height="30" fill="#005A9E" rx="4" />
      
      <!-- 观众3 -->
      <circle cx="150" cy="60" r="18" fill="#FF6B6B" />
      <rect x="140" y="78" width="20" height="30" fill="#333333" rx="4" />
      
      <text x="100" y="140" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="14px" fill="#333333">
        漫步观摩
      </text>
    </g>
  </g>
  
  <!-- 总结与预告 -->
  <g transform="translate(200, 820)">
    <rect x="0" y="0" width="1520" height="180" fill="#005A9E" rx="15" />
    <text x="760" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="28px" font-weight="bold" fill="#FFFFFF">
      今天下午，我们完成了从"诊断"到"开方"的全过程
    </text>
    <text x="760" y="80" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="24px" fill="#FFFFFF">
      我们手里的"增长药方"已经初具雏形
    </text>
    
    <rect x="100" y="100" width="1320" height="60" fill="#F5A623" rx="10" />
    <text x="760" y="125" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="22px" font-weight="bold" fill="#FFFFFF">
      但是，一张再好的药方，如果不能让病人理解并接受，也只是一张废纸
    </text>
    <text x="760" y="150" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="20px" fill="#FFFFFF">
      如何将逻辑严密的"画布"，变成让客户心潮澎湃、无法抗拒的"商业大片"？
    </text>
  </g>
  
  <!-- 画廊图标 -->
  <g transform="translate(100, 380)">
    <!-- 画框 -->
    <rect x="20" y="20" width="80" height="60" fill="#FFFFFF" stroke="#F5A623" stroke-width="4" rx="5" />
    <rect x="30" y="30" width="60" height="40" fill="#E6F3FF" rx="3" />
    
    <!-- 画作内容 -->
    <rect x="35" y="35" width="50" height="5" fill="#2196F3" rx="1" />
    <rect x="35" y="45" width="30" height="3" fill="#4CAF50" rx="1" />
    <rect x="35" y="52" width="40" height="3" fill="#FF9800" rx="1" />
    <rect x="35" y="59" width="25" height="3" fill="#9C27B0" rx="1" />
    
    <text x="60" y="105" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      方案画廊
    </text>
  </g>
  
  <!-- 便利贴图标 -->
  <g transform="translate(1700, 380)">
    <!-- 便利贴堆叠 -->
    <rect x="30" y="35" width="50" height="40" fill="#FFD700" rx="3" />
    <rect x="25" y="30" width="50" height="40" fill="#FFE4E1" rx="3" />
    <rect x="20" y="25" width="50" height="40" fill="#E8F5E8" rx="3" />
    
    <!-- 文字 -->
    <text x="45" y="40" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8px" fill="#333333">
      很棒！
    </text>
    <text x="45" y="50" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="8px" fill="#333333">
      有问题
    </text>
    
    <text x="45" y="90" text-anchor="middle" font-family="Microsoft YaHei, 微软雅黑, sans-serif" font-size="12px" fill="#333333">
      互动评价
    </text>
  </g>
</svg>
